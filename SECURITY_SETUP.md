# 🔒 Security Setup Guide

## ⚠️ CRITICAL: Remove Sensitive Files Before Pushing

Your repository contains sensitive information that must be secured before pushing to GitHub.

## 🚨 Immediate Actions Required

### 1. Remove Sensitive Files from Git Tracking

Run these commands in your terminal:

```bash
# Remove sensitive files from git tracking (but keep them locally)
git rm --cached .env
git rm --cached android/key.properties
git rm --cached android/local.properties
git rm --cached android/upload-keystore.jks
git rm --cached android/app/google-services.json

# If iOS GoogleService-Info.plist exists, remove it too
git rm --cached ios/Runner/GoogleService-Info.plist

# Remove any postman collection files with API keys
git rm --cached lib/postman*.json
```

### 2. Add and Commit Your Changes

```bash
# Add all files (sensitive ones are now ignored)
git add .

# Commit your changes
git commit -m "Initial commit with security fixes

- Added comprehensive .gitignore
- Removed sensitive files from tracking
- Added .env.example template
- Secured API keys and credentials"

# Push to GitHub
git push -u origin main
```

## 🔐 Sensitive Files Identified

### Environment Variables (.env)
- **MongoDB URI**: `mongodb+srv://prav59632:<EMAIL>/`
- **JWT Secret**: `hdjdjkolso12339nfhf@1!u`
- **AWS Keys**: `********************` / `QkK0PwCZDjPnZOyjfBMPE6VlZWq5HNeb0KTF9KEw`
- **Admin Credentials**: `<EMAIL>` / `admin123`

### Android Signing
- `android/key.properties` - Contains keystore passwords
- `android/upload-keystore.jks` - App signing keystore
- `android/local.properties` - Local SDK paths

### Firebase Configuration
- `android/app/google-services.json` - Firebase project config
- `ios/Runner/GoogleService-Info.plist` - iOS Firebase config

## 🛡️ Security Best Practices

### 1. Environment Variables
- ✅ Use `.env.example` with placeholder values
- ✅ Never commit actual `.env` files
- ✅ Use different credentials for development/production

### 2. API Keys & Secrets
- 🔄 **ROTATE ALL EXPOSED CREDENTIALS IMMEDIATELY**
- 🔄 Change MongoDB password
- 🔄 Generate new JWT secret
- 🔄 Rotate AWS access keys
- 🔄 Update admin password

### 3. Firebase Security
- 🔄 Regenerate Firebase configuration
- ✅ Use Firebase security rules
- ✅ Enable App Check for production

### 4. Android Signing
- ✅ Keep keystores secure and backed up
- ✅ Use different keystores for debug/release
- ✅ Never commit keystore files

## 🔧 Setup Instructions for New Developers

1. **Clone the repository**
   ```bash
   git clone https://github.com/praveen-singh01/English_Guru.git
   cd English_Guru/seekho_flutter_app
   ```

2. **Create environment file**
   ```bash
   cp .env.example .env
   # Edit .env with actual values
   ```

3. **Setup Firebase**
   - Download `google-services.json` from Firebase Console
   - Place in `android/app/` directory
   - Download `GoogleService-Info.plist` for iOS
   - Place in `ios/Runner/` directory

4. **Setup Android signing**
   - Create `android/key.properties` with keystore info
   - Place keystore file in `android/` directory

## 📋 Updated .gitignore Coverage

The `.gitignore` now includes:
- ✅ Environment variables (`.env*`)
- ✅ Firebase configuration files
- ✅ Android signing files
- ✅ API keys and secrets
- ✅ Build artifacts
- ✅ IDE files
- ✅ Temporary files
- ✅ Database files
- ✅ Backup files

## 🚀 Safe Deployment Checklist

- [ ] All sensitive files removed from git
- [ ] Credentials rotated
- [ ] Environment variables configured on server
- [ ] Firebase security rules enabled
- [ ] API rate limiting configured
- [ ] HTTPS enforced
- [ ] Database access restricted
- [ ] Monitoring and logging enabled

## 📞 Emergency Response

If sensitive data was already pushed:
1. **Immediately rotate all credentials**
2. **Force push with cleaned history** (if no collaborators)
3. **Contact GitHub support** to purge sensitive data
4. **Monitor for unauthorized access**

## 🔗 Additional Resources

- [GitHub Security Best Practices](https://docs.github.com/en/code-security)
- [Flutter Security Guidelines](https://flutter.dev/docs/deployment/security)
- [Firebase Security Rules](https://firebase.google.com/docs/rules)
