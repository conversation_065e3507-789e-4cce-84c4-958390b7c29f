import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:bolo_app/models/category.dart';
import 'package:bolo_app/models/video.dart';
import 'package:bolo_app/screens/category_detail_screen.dart';
import 'package:bolo_app/screens/video_detail_screen.dart';

void main() {
  group('Navigation Flow Tests', () {
    // Create test data
    final testCategory = Category(
      id: 'test-category-1',
      name: 'Part Time Income',
      description: 'Learn about part-time income opportunities',
      order: 1,
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    final testVideo = Video(
      id: 'test-video-1',
      title: 'Golden Surveys App Se Kamai',
      description: 'Learn how to earn money using Golden Surveys app',
      url: 'https://example.com/video1.mp4',
      thumbnail: 'https://example.com/thumbnail1.jpg',
      duration: 120,
      views: 1500,
      categoryId: 'test-category-1',
      topicId: 'test-topic-1',
      isPremium: false,
      order: 1,
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    final testVideos = [testVideo];

    testWidgets('CategoryDetailScreen should display category name and videos', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: CategoryDetailScreen(category: testCategory),
        ),
      );

      // Wait for the widget to build
      await tester.pump();

      // Verify category name is displayed in app bar
      expect(find.text('Part Time Income'), findsOneWidget);

      // Verify app bar actions are present
      expect(find.byIcon(Icons.star_border), findsOneWidget);
      expect(find.byIcon(Icons.search), findsOneWidget);
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
    });

    testWidgets('VideoDetailScreen should display video information', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VideoDetailScreen(
            video: testVideo,
            category: testCategory,
            allVideos: testVideos,
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pump();

      // Verify video title is displayed
      expect(find.text('Golden Surveys App Se Kamai'), findsOneWidget);

      // Verify play button is present
      expect(find.text('Play'), findsOneWidget);
      expect(find.byIcon(Icons.play_arrow), findsAtLeastNWidget(1));

      // Verify all episodes section
      expect(find.text('All Episodes (1)'), findsOneWidget);
    });

    testWidgets('CategoryDetailScreen navigation buttons should work', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: CategoryDetailScreen(category: testCategory),
        ),
      );

      await tester.pump();

      // Test star button
      await tester.tap(find.byIcon(Icons.star_border));
      await tester.pump();
      expect(find.text('Favorites feature coming soon!'), findsOneWidget);

      // Dismiss the snackbar
      await tester.pump(const Duration(seconds: 1));

      // Test search button
      await tester.tap(find.byIcon(Icons.search));
      await tester.pump();
      expect(find.text('Search feature coming soon!'), findsOneWidget);
    });

    testWidgets('VideoDetailScreen play button should show message', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VideoDetailScreen(
            video: testVideo,
            category: testCategory,
            allVideos: testVideos,
          ),
        ),
      );

      await tester.pump();

      // Test play button
      await tester.tap(find.text('Play'));
      await tester.pump();
      expect(find.text('Video player will be implemented'), findsOneWidget);
    });
  });

  group('Model Tests', () {
    testWidgets('Category model should serialize correctly', (WidgetTester tester) async {
      final category = Category(
        id: 'test-id',
        name: 'Test Category',
        description: 'Test Description',
        order: 1,
        isActive: true,
        createdAt: DateTime(2024, 1, 1),
        updatedAt: DateTime(2024, 1, 2),
      );

      final json = category.toJson();
      expect(json['_id'], equals('test-id'));
      expect(json['name'], equals('Test Category'));
      expect(json['description'], equals('Test Description'));
      expect(json['order'], equals(1));
      expect(json['isActive'], equals(true));

      final fromJson = Category.fromJson(json);
      expect(fromJson.id, equals(category.id));
      expect(fromJson.name, equals(category.name));
      expect(fromJson.description, equals(category.description));
    });

    testWidgets('Video model should serialize correctly', (WidgetTester tester) async {
      final video = Video(
        id: 'test-video-id',
        title: 'Test Video',
        description: 'Test Description',
        url: 'https://example.com/video.mp4',
        thumbnail: 'https://example.com/thumb.jpg',
        duration: 120,
        views: 100,
        categoryId: 'cat-id',
        topicId: 'topic-id',
        isPremium: false,
        order: 1,
        isActive: true,
        createdAt: DateTime(2024, 1, 1),
        updatedAt: DateTime(2024, 1, 2),
      );

      final json = video.toJson();
      expect(json['_id'], equals('test-video-id'));
      expect(json['title'], equals('Test Video'));
      expect(json['url'], equals('https://example.com/video.mp4'));
      expect(json['duration'], equals(120));
      expect(json['views'], equals(100));
      expect(json['isPremium'], equals(false));

      final fromJson = Video.fromJson(json);
      expect(fromJson.id, equals(video.id));
      expect(fromJson.title, equals(video.title));
      expect(fromJson.url, equals(video.url));
      expect(fromJson.duration, equals(video.duration));
    });
  });
}
