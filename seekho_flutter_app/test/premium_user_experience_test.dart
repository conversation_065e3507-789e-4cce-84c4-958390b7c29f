import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:bolo_app/main.dart';
import 'package:bolo_app/services/new_subscription_service.dart';
import 'package:bolo_app/services/auth_service.dart';
import 'package:bolo_app/models/user.dart';

void main() {
  group('Premium User Experience Tests', () {
    testWidgets('Premium users should skip onboarding and go directly to main content', (WidgetTester tester) async {
      // This test verifies that premium users with completed onboarding skip subscription intro
      
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();
      
      // Mock premium user login
      // In actual implementation, mock AuthService and NewSubscriptionService
      // to simulate premium user login flow
      
      // Expected: Premium user should land on MainContentScreen, not SubscriptionIntroScreen
      expect(find.text('👑 PREMIUM'), findsOneWidget);
      expect(find.text('⚡ PLUS'), findsNothing);
    });

    testWidgets('Premium users should see premium badge instead of PLUS badge', (WidgetTester tester) async {
      // This test verifies that premium users see premium badge in main content screen
      
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();
      
      // Navigate to main content screen
      // Verify premium badge is shown
      expect(find.text('👑 PREMIUM'), findsOneWidget);
      expect(find.text('⚡ PLUS'), findsNothing);
      
      // Verify premium badge is not clickable (no subscription navigation)
      final premiumBadge = find.text('👑 PREMIUM');
      expect(premiumBadge, findsOneWidget);
    });

    testWidgets('Premium users should see premium status in profile', (WidgetTester tester) async {
      // This test verifies that premium users see their subscription status in profile
      
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();
      
      // Navigate to profile screen
      // Look for premium status section
      expect(find.text('Premium Member'), findsOneWidget);
      expect(find.text('ACTIVE'), findsOneWidget);
      expect(find.text('Unlimited Access'), findsOneWidget);
      expect(find.text('No Ads'), findsOneWidget);
      
      // Should not see upgrade CTA
      expect(find.text('Upgrade to Premium'), findsNothing);
      expect(find.text('Upgrade Now'), findsNothing);
    });

    testWidgets('Free users should see upgrade options in profile', (WidgetTester tester) async {
      // This test verifies that free users see upgrade options in profile
      
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();
      
      // Mock free user
      // Navigate to profile screen
      // Look for upgrade section
      expect(find.text('Upgrade to Premium'), findsOneWidget);
      expect(find.text('Upgrade Now'), findsOneWidget);
      
      // Should not see premium status
      expect(find.text('Premium Member'), findsNothing);
      expect(find.text('ACTIVE'), findsNothing);
    });

    testWidgets('Premium users should not see subscription intro screen', (WidgetTester tester) async {
      // This test verifies that premium users skip subscription intro after onboarding
      
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();
      
      // Mock premium user completing onboarding
      // Verify they go directly to main content, not subscription intro
      expect(find.byType(MainContentScreen), findsOneWidget);
      expect(find.byType(SubscriptionIntroScreen), findsNothing);
    });

    testWidgets('Premium lock icons should not appear for premium users', (WidgetTester tester) async {
      // This test verifies that premium content shows no lock icons for premium users
      
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();
      
      // Navigate to content with premium items
      // Verify no lock icons are shown
      expect(find.byIcon(Icons.lock), findsNothing);
      
      // Verify premium content is accessible without prompts
    });

    test('Premium user detection works correctly', () async {
      // Test premium user email detection
      
      const premiumEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>', // Case insensitive
      ];
      
      for (final email in premiumEmails) {
        // Mock user with premium email
        final user = User(
          id: 'test-id',
          name: 'Test User',
          email: email,
          role: 'user',
          createdAt: DateTime.now(),
          isOnboardingComplete: true,
        );
        
        // In actual test, mock AuthService.getStoredUser() to return this user
        // Then verify NewSubscriptionService.hasAccessToPremiumContent() returns true
        expect(user.email.toLowerCase(), contains('@gmail.com'));
      }
    });

    testWidgets('Onboarding completion routes correctly for premium users', (WidgetTester tester) async {
      // This test verifies onboarding completion flow for premium users
      
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();
      
      // Mock user completing onboarding
      // If premium: should go to MainContentScreen
      // If free: should go to SubscriptionIntroScreen
      
      // For premium users
      expect(find.byType(MainContentScreen), findsOneWidget);
      
      // For free users (separate test case)
      // expect(find.byType(SubscriptionIntroScreen), findsOneWidget);
    });
  });

  group('Free User Experience Tests', () {
    testWidgets('Free users should see PLUS badge and upgrade prompts', (WidgetTester tester) async {
      // This test verifies that free users see appropriate upgrade prompts
      
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();
      
      // Mock free user
      // Verify PLUS badge is shown and clickable
      expect(find.text('⚡ PLUS'), findsOneWidget);
      expect(find.text('👑 PREMIUM'), findsNothing);
      
      // Tap PLUS badge should navigate to subscription screen
      await tester.tap(find.text('⚡ PLUS'));
      await tester.pumpAndSettle();
      
      // Should see subscription screen
      expect(find.byType(UnifiedSubscriptionScreen), findsOneWidget);
    });

    testWidgets('Free users should see subscription intro after onboarding', (WidgetTester tester) async {
      // This test verifies that free users see subscription intro after completing onboarding
      
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();
      
      // Mock free user completing onboarding
      // Should navigate to subscription intro screen
      expect(find.byType(SubscriptionIntroScreen), findsOneWidget);
      expect(find.byType(MainContentScreen), findsNothing);
    });
  });

  group('Navigation and Routing Tests', () {
    testWidgets('Login routing works correctly for different user types', (WidgetTester tester) async {
      // This test verifies login routing logic
      
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();
      
      // Test different user scenarios:
      // 1. New user (no onboarding) -> OnboardingScreen
      // 2. Free user (onboarding complete) -> SubscriptionIntroScreen
      // 3. Premium user (onboarding complete) -> MainContentScreen
      
      // This would require mocking different user states
    });
  });
}

// Helper classes for testing
class MockUser {
  static User createPremiumUser({String email = '<EMAIL>'}) {
    return User(
      id: 'premium-user-id',
      name: 'Premium User',
      email: email,
      role: 'user',
      createdAt: DateTime.now(),
      isOnboardingComplete: true,
    );
  }

  static User createFreeUser({String email = '<EMAIL>'}) {
    return User(
      id: 'free-user-id',
      name: 'Free User',
      email: email,
      role: 'user',
      createdAt: DateTime.now(),
      isOnboardingComplete: true,
    );
  }

  static User createNewUser({String email = '<EMAIL>'}) {
    return User(
      id: 'new-user-id',
      name: 'New User',
      email: email,
      role: 'user',
      createdAt: DateTime.now(),
      isOnboardingComplete: false,
    );
  }
}

// Mock classes for testing (would be implemented with proper mocking framework)
class MockAuthService {
  static User? _currentUser;
  
  static void setCurrentUser(User user) {
    _currentUser = user;
  }
  
  static Future<User?> getStoredUser() async {
    return _currentUser;
  }
}

class MockNewSubscriptionService {
  static Future<bool> hasAccessToPremiumContent() async {
    final user = await MockAuthService.getStoredUser();
    if (user == null) return false;
    
    const premiumEmails = [
      '<EMAIL>',
      '<EMAIL>',
    ];
    
    return premiumEmails.contains(user.email.toLowerCase());
  }
}
