class Video {
  final String id;
  final String title;
  final String description;
  final String? thumbnail;
  final String? videoUrl;
  final int duration; // in seconds
  final bool isPremium;
  final bool isActive;
  final String topicId;
  final String categoryId;
  final int order;
  final int views;
  final DateTime createdAt;
  final DateTime updatedAt;

  Video({
    required this.id,
    required this.title,
    required this.description,
    this.thumbnail,
    this.videoUrl,
    required this.duration,
    required this.isPremium,
    required this.isActive,
    required this.topicId,
    required this.categoryId,
    required this.order,
    required this.views,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Video.fromJson(Map<String, dynamic> json) {
    return Video(
      id: json['_id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      thumbnail: json['thumbnail'],
      videoUrl: json['videoUrl'],
      duration: json['duration'] ?? 0,
      isPremium: json['isPremium'] ?? true, // Default to premium for testing
      isActive: json['isActive'] ?? true,
      topicId: json['topicId'] ?? '',
      categoryId: json['categoryId'] ?? '',
      order: json['order'] ?? 0,
      views: json['views'] ?? 0,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
      'description': description,
      'thumbnail': thumbnail,
      'videoUrl': videoUrl,
      'duration': duration,
      'isPremium': isPremium,
      'isActive': isActive,
      'topicId': topicId,
      'categoryId': categoryId,
      'order': order,
      'views': views,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  String get formattedDuration {
    final minutes = duration ~/ 60;
    final seconds = duration % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}

class VideosResponse {
  final bool success;
  final String message;
  final List<Video>? data;

  VideosResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory VideosResponse.fromJson(Map<String, dynamic> json) {
    return VideosResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null
          ? (json['data'] as List).map((item) => Video.fromJson(item)).toList()
          : null,
    );
  }
}
