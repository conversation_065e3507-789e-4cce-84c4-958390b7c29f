class QuestionnaireOption {
  final String id;
  final String text;

  QuestionnaireOption({
    required this.id,
    required this.text,
  });

  factory QuestionnaireOption.fromJson(Map<String, dynamic> json) {
    return QuestionnaireOption(
      id: json['id'] ?? json['_id'] ?? '',
      text: json['text'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
    };
  }
}

enum QuestionType {
  multipleChoice,
  textInput,
  trueFalse,
  shortAnswer,
}

class QuestionnaireQuestion {
  final String id;
  final String questionText;
  final QuestionType questionType;
  final List<String> options;
  final dynamic correctAnswer; // Can be int for MCQ, String for text, bool for true/false
  final String explanation;
  final List<String> hints;
  final int order;
  final String difficulty;
  final int points;
  final bool isRequired;
  final int? maxLength; // For text input questions

  QuestionnaireQuestion({
    required this.id,
    required this.questionText,
    required this.questionType,
    required this.options,
    required this.correctAnswer,
    required this.explanation,
    required this.hints,
    required this.order,
    required this.difficulty,
    required this.points,
    this.isRequired = true,
    this.maxLength,
  });

  factory QuestionnaireQuestion.fromJson(Map<String, dynamic> json) {
    // Determine question type from the data
    QuestionType type = QuestionType.multipleChoice;
    final typeString = json['questionType'] ?? json['type'] ?? '';

    switch (typeString.toLowerCase()) {
      case 'text':
      case 'textinput':
      case 'text_input':
      case 'long_answer':
      case 'longanswer':
        type = QuestionType.textInput;
        break;
      case 'truefalse':
      case 'true_false':
      case 'boolean':
        type = QuestionType.trueFalse;
        break;
      case 'short':
      case 'shortanswer':
      case 'short_answer':
        type = QuestionType.shortAnswer;
        break;
      default:
        type = QuestionType.multipleChoice;
    }

    // If no explicit type, determine from options
    final options = List<String>.from(json['options'] ?? []);
    if (typeString.isEmpty) {
      if (options.isNotEmpty) {
        type = QuestionType.multipleChoice;
      } else {
        // No options means it's a text input question
        type = QuestionType.textInput;
      }
    }

    return QuestionnaireQuestion(
      id: json['id'] ?? json['_id'] ?? '',
      questionText: json['questionText'] ?? '',
      questionType: type,
      options: options,
      correctAnswer: json['correctAnswer'],
      explanation: json['explanation'] ?? '',
      hints: List<String>.from(json['hints'] ?? []),
      order: json['order'] ?? 0,
      difficulty: json['difficulty'] ?? 'easy',
      points: json['points'] ?? 1,
      isRequired: json['isRequired'] ?? true,
      maxLength: json['maxLength'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'questionText': questionText,
      'questionType': questionType.toString().split('.').last,
      'options': options,
      'correctAnswer': correctAnswer,
      'explanation': explanation,
      'hints': hints,
      'order': order,
      'difficulty': difficulty,
      'points': points,
      'isRequired': isRequired,
      'maxLength': maxLength,
    };
  }
}

class QuestionnaireTopic {
  final String id;
  final String title;
  final String slug;

  QuestionnaireTopic({
    required this.id,
    required this.title,
    required this.slug,
  });

  factory QuestionnaireTopic.fromJson(Map<String, dynamic> json) {
    return QuestionnaireTopic(
      id: json['id'] ?? json['_id'] ?? '',
      title: json['title'] ?? '',
      slug: json['slug'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'slug': slug,
    };
  }
}

class QuestionnaireMetadata {
  final int totalQuestions;
  final int totalAttempts;
  final double averageScore;
  final double averageCompletionTime;
  final double passRate;

  QuestionnaireMetadata({
    required this.totalQuestions,
    required this.totalAttempts,
    required this.averageScore,
    required this.averageCompletionTime,
    required this.passRate,
  });

  factory QuestionnaireMetadata.fromJson(Map<String, dynamic> json) {
    return QuestionnaireMetadata(
      totalQuestions: json['totalQuestions'] ?? 0,
      totalAttempts: json['totalAttempts'] ?? 0,
      averageScore: (json['averageScore'] ?? 0).toDouble(),
      averageCompletionTime: (json['averageCompletionTime'] ?? 0).toDouble(),
      passRate: (json['passRate'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalQuestions': totalQuestions,
      'totalAttempts': totalAttempts,
      'averageScore': averageScore,
      'averageCompletionTime': averageCompletionTime,
      'passRate': passRate,
    };
  }
}

class QuestionnaireData {
  final String id;
  final String packageId;
  final String title;
  final String description;
  final QuestionnaireTopic topic;
  final List<QuestionnaireQuestion> questions;
  final String difficulty;
  final int estimatedTime;
  final int passingScore;
  final bool isActive;
  final bool isPremium;
  final int order;
  final List<String> tags;
  final QuestionnaireMetadata metadata;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String slug;
  final bool hasAccess;

  QuestionnaireData({
    required this.id,
    required this.packageId,
    required this.title,
    required this.description,
    required this.topic,
    required this.questions,
    required this.difficulty,
    required this.estimatedTime,
    required this.passingScore,
    required this.isActive,
    required this.isPremium,
    required this.order,
    required this.tags,
    required this.metadata,
    required this.createdAt,
    required this.updatedAt,
    required this.slug,
    required this.hasAccess,
  });

  factory QuestionnaireData.fromJson(Map<String, dynamic> json) {
    return QuestionnaireData(
      id: json['id'] ?? json['_id'] ?? '',
      packageId: json['packageId'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      topic: QuestionnaireTopic.fromJson(json['topic'] ?? {}),
      questions: (json['questions'] as List<dynamic>?)
              ?.map((question) => QuestionnaireQuestion.fromJson(question))
              .toList() ??
          [],
      difficulty: json['difficulty'] ?? 'beginner',
      estimatedTime: json['estimatedTime'] ?? 0,
      passingScore: json['passingScore'] ?? 70,
      isActive: json['isActive'] ?? true,
      isPremium: json['isPremium'] ?? false,
      order: json['order'] ?? 0,
      tags: List<String>.from(json['tags'] ?? []),
      metadata: QuestionnaireMetadata.fromJson(json['metadata'] ?? {}),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      slug: json['slug'] ?? '',
      hasAccess: json['hasAccess'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'packageId': packageId,
      'title': title,
      'description': description,
      'topic': topic.toJson(),
      'questions': questions.map((q) => q.toJson()).toList(),
      'difficulty': difficulty,
      'estimatedTime': estimatedTime,
      'passingScore': passingScore,
      'isActive': isActive,
      'isPremium': isPremium,
      'order': order,
      'tags': tags,
      'metadata': metadata.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'slug': slug,
      'hasAccess': hasAccess,
    };
  }
}

class QuestionnaireAnswer {
  final String questionId;
  final dynamic answer; // Can be int for MCQ, String for text, bool for true/false
  final bool isCorrect;
  final DateTime answeredAt;

  QuestionnaireAnswer({
    required this.questionId,
    required this.answer,
    required this.isCorrect,
    required this.answeredAt,
  });

  // Backward compatibility getters
  int get selectedOption => answer is int ? answer : -1;
  String get textAnswer => answer is String ? answer : '';
  bool get boolAnswer => answer is bool ? answer : false;

  Map<String, dynamic> toJson() {
    return {
      'questionId': questionId,
      'answer': answer,
      'selectedOption': selectedOption, // Keep for backward compatibility
      'isCorrect': isCorrect,
      'answeredAt': answeredAt.toIso8601String(),
    };
  }
}

class QuestionnaireResult {
  final String questionnaireId;
  final int score;
  final int totalQuestions;
  final int correctAnswers;
  final int timeTaken;
  final bool passed;
  final List<QuestionnaireAnswer> answers;
  final DateTime completedAt;

  QuestionnaireResult({
    required this.questionnaireId,
    required this.score,
    required this.totalQuestions,
    required this.correctAnswers,
    required this.timeTaken,
    required this.passed,
    required this.answers,
    required this.completedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'questionnaireId': questionnaireId,
      'score': score,
      'totalQuestions': totalQuestions,
      'correctAnswers': correctAnswers,
      'timeTaken': timeTaken,
      'passed': passed,
      'answers': answers.map((a) => a.toJson()).toList(),
      'completedAt': completedAt.toIso8601String(),
    };
  }
}

class QuestionnaireSubmissionAnswer {
  final int questionIndex;
  final String questionText;
  final String? textAnswer;
  final int? selectedOption;
  final bool? boolAnswer;
  final int timeSpent;

  QuestionnaireSubmissionAnswer({
    required this.questionIndex,
    required this.questionText,
    this.textAnswer,
    this.selectedOption,
    this.boolAnswer,
    required this.timeSpent,
  });

  factory QuestionnaireSubmissionAnswer.fromJson(Map<String, dynamic> json) {
    return QuestionnaireSubmissionAnswer(
      questionIndex: json['questionIndex'] ?? 0,
      questionText: json['questionText'] ?? '',
      textAnswer: json['textAnswer'],
      selectedOption: json['selectedOption'],
      boolAnswer: json['boolAnswer'],
      timeSpent: json['timeSpent'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'questionIndex': questionIndex,
      'questionText': questionText,
      if (textAnswer != null) 'textAnswer': textAnswer,
      if (selectedOption != null) 'selectedOption': selectedOption,
      if (boolAnswer != null) 'boolAnswer': boolAnswer,
      'timeSpent': timeSpent,
    };
  }
}

class QuestionnaireSubmissionResult {
  final String submissionId;
  final DateTime completedAt;
  final int completionTime;
  final int totalQuestions;
  final int answeredQuestions;
  final int correctAnswers;
  final int score;
  final bool passed;
  final String feedback;
  final List<QuestionnaireSubmissionAnswer> answers;

  QuestionnaireSubmissionResult({
    required this.submissionId,
    required this.completedAt,
    required this.completionTime,
    required this.totalQuestions,
    required this.answeredQuestions,
    required this.correctAnswers,
    required this.score,
    required this.passed,
    required this.feedback,
    required this.answers,
  });

  factory QuestionnaireSubmissionResult.fromJson(Map<String, dynamic> json) {
    return QuestionnaireSubmissionResult(
      submissionId: json['submissionId'] ?? '',
      completedAt: DateTime.parse(json['completedAt'] ?? DateTime.now().toIso8601String()),
      completionTime: json['completionTime'] ?? 0,
      totalQuestions: json['totalQuestions'] ?? 0,
      answeredQuestions: json['answeredQuestions'] ?? 0,
      correctAnswers: json['correctAnswers'] ?? 0,
      score: json['score'] ?? 0,
      passed: json['passed'] ?? false,
      feedback: json['feedback'] ?? '',
      answers: (json['answers'] as List<dynamic>?)
              ?.map((answer) => QuestionnaireSubmissionAnswer.fromJson(answer))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'submissionId': submissionId,
      'completedAt': completedAt.toIso8601String(),
      'completionTime': completionTime,
      'totalQuestions': totalQuestions,
      'answeredQuestions': answeredQuestions,
      'correctAnswers': correctAnswers,
      'score': score,
      'passed': passed,
      'feedback': feedback,
      'answers': answers.map((a) => a.toJson()).toList(),
    };
  }
}
