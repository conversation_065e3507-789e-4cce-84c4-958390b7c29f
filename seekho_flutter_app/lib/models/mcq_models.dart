class MCQOption {
  final String id;
  final String text;

  MCQOption({
    required this.id,
    required this.text,
  });

  factory MCQOption.fromJson(Map<String, dynamic> json) {
    return MCQOption(
      id: json['id'] ?? json['_id'] ?? '',
      text: json['text'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
    };
  }
}

class MCQQuestion {
  final String id;
  final String questionText;
  final List<MCQOption> options;
  final String explanation;
  final List<String> hints;
  final int order;
  final String difficulty;
  final int points;

  MCQQuestion({
    required this.id,
    required this.questionText,
    required this.options,
    required this.explanation,
    required this.hints,
    required this.order,
    required this.difficulty,
    required this.points,
  });

  factory MCQQuestion.fromJson(Map<String, dynamic> json) {
    return MCQQuestion(
      id: json['id'] ?? json['_id'] ?? '',
      questionText: json['questionText'] ?? '',
      options: (json['options'] as List<dynamic>?)
              ?.map((option) => MCQOption.fromJson(option))
              .toList() ??
          [],
      explanation: json['explanation'] ?? '',
      hints: List<String>.from(json['hints'] ?? []),
      order: json['order'] ?? 0,
      difficulty: json['difficulty'] ?? 'easy',
      points: json['points'] ?? 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'questionText': questionText,
      'options': options.map((o) => o.toJson()).toList(),
      'explanation': explanation,
      'hints': hints,
      'order': order,
      'difficulty': difficulty,
      'points': points,
    };
  }
}

class MCQTopic {
  final String id;
  final String title;
  final String slug;

  MCQTopic({
    required this.id,
    required this.title,
    required this.slug,
  });

  factory MCQTopic.fromJson(Map<String, dynamic> json) {
    return MCQTopic(
      id: json['id'] ?? json['_id'] ?? '',
      title: json['title'] ?? '',
      slug: json['slug'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'slug': slug,
    };
  }
}

class MCQMetadata {
  final int totalQuestions;
  final int totalAttempts;
  final double averageScore;
  final double averageCompletionTime;
  final double passRate;

  MCQMetadata({
    required this.totalQuestions,
    required this.totalAttempts,
    required this.averageScore,
    required this.averageCompletionTime,
    required this.passRate,
  });

  factory MCQMetadata.fromJson(Map<String, dynamic> json) {
    return MCQMetadata(
      totalQuestions: json['totalQuestions'] ?? 0,
      totalAttempts: json['totalAttempts'] ?? 0,
      averageScore: (json['averageScore'] ?? 0).toDouble(),
      averageCompletionTime: (json['averageCompletionTime'] ?? 0).toDouble(),
      passRate: (json['passRate'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalQuestions': totalQuestions,
      'totalAttempts': totalAttempts,
      'averageScore': averageScore,
      'averageCompletionTime': averageCompletionTime,
      'passRate': passRate,
    };
  }
}

class MCQData {
  final String id;
  final String packageId;
  final String title;
  final String description;
  final MCQTopic topic;
  final List<MCQQuestion> questions;
  final String difficulty;
  final int estimatedTime;
  final int passingScore;
  final bool isActive;
  final bool isPremium;
  final int order;
  final List<String> tags;
  final MCQMetadata metadata;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String slug;
  final bool hasAccess;

  MCQData({
    required this.id,
    required this.packageId,
    required this.title,
    required this.description,
    required this.topic,
    required this.questions,
    required this.difficulty,
    required this.estimatedTime,
    required this.passingScore,
    required this.isActive,
    required this.isPremium,
    required this.order,
    required this.tags,
    required this.metadata,
    required this.createdAt,
    required this.updatedAt,
    required this.slug,
    required this.hasAccess,
  });

  factory MCQData.fromJson(Map<String, dynamic> json) {
    return MCQData(
      id: json['id'] ?? json['_id'] ?? '',
      packageId: json['packageId'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      topic: MCQTopic.fromJson(json['topic'] ?? {}),
      questions: (json['questions'] as List<dynamic>?)
              ?.map((question) => MCQQuestion.fromJson(question))
              .toList() ??
          [],
      difficulty: json['difficulty'] ?? 'beginner',
      estimatedTime: json['estimatedTime'] ?? 0,
      passingScore: json['passingScore'] ?? 70,
      isActive: json['isActive'] ?? true,
      isPremium: json['isPremium'] ?? false,
      order: json['order'] ?? 0,
      tags: List<String>.from(json['tags'] ?? []),
      metadata: MCQMetadata.fromJson(json['metadata'] ?? {}),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      slug: json['slug'] ?? '',
      hasAccess: json['hasAccess'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'packageId': packageId,
      'title': title,
      'description': description,
      'topic': topic.toJson(),
      'questions': questions.map((q) => q.toJson()).toList(),
      'difficulty': difficulty,
      'estimatedTime': estimatedTime,
      'passingScore': passingScore,
      'isActive': isActive,
      'isPremium': isPremium,
      'order': order,
      'tags': tags,
      'metadata': metadata.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'slug': slug,
      'hasAccess': hasAccess,
    };
  }
}

class MCQAnswer {
  final String questionId;
  final String selectedOptionId;
  final bool isCorrect;
  final DateTime answeredAt;

  MCQAnswer({
    required this.questionId,
    required this.selectedOptionId,
    required this.isCorrect,
    required this.answeredAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'questionId': questionId,
      'selectedOptionId': selectedOptionId,
      'isCorrect': isCorrect,
      'answeredAt': answeredAt.toIso8601String(),
    };
  }
}

class MCQResult {
  final String mcqId;
  final List<MCQAnswer> answers;
  final int score;
  final int totalQuestions;
  final double percentage;
  final bool passed;
  final int timeTaken; // in seconds
  final DateTime completedAt;

  MCQResult({
    required this.mcqId,
    required this.answers,
    required this.score,
    required this.totalQuestions,
    required this.percentage,
    required this.passed,
    required this.timeTaken,
    required this.completedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'mcqId': mcqId,
      'answers': answers.map((answer) => answer.toJson()).toList(),
      'score': score,
      'totalQuestions': totalQuestions,
      'percentage': percentage,
      'passed': passed,
      'timeTaken': timeTaken,
      'completedAt': completedAt.toIso8601String(),
    };
  }
}
