class User {
  final String id;
  final String email;
  final String name;
  final String? profilePicture;
  final String role;
  final DateTime createdAt;
  final int followers;
  final int following;
  final int lessons;
  final int views;
  final int series;
  final int videos;

  // Onboarding fields
  final String? phoneNumber;
  final int? classLevel;
  final int? age;
  final bool isOnboardingComplete;

  User({
    required this.id,
    required this.email,
    required this.name,
    this.profilePicture,
    required this.role,
    required this.createdAt,
    this.followers = 0,
    this.following = 0,
    this.lessons = 0,
    this.views = 0,
    this.series = 0,
    this.videos = 0,
    this.phoneNumber,
    this.classLevel,
    this.age,
    this.isOnboardingComplete = false,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['_id'] ?? json['id'] ?? '',
      email: json['email'] ?? '',
      name: json['name'] ?? '',
      profilePicture: json['profilePicture'],
      role: json['role'] ?? 'user',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      followers: json['followers'] ?? 0,
      following: json['following'] ?? 0,
      lessons: json['lessons'] ?? 0,
      views: json['views'] ?? 0,
      series: json['series'] ?? 0,
      videos: json['videos'] ?? 0,
      phoneNumber: json['phoneNumber'],
      classLevel: json['classLevel'],
      age: json['age'],
      isOnboardingComplete: json['isOnboardingComplete'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'profilePicture': profilePicture,
      'role': role,
      'createdAt': createdAt.toIso8601String(),
      'followers': followers,
      'following': following,
      'lessons': lessons,
      'views': views,
      'series': series,
      'videos': videos,
      'phoneNumber': phoneNumber,
      'classLevel': classLevel,
      'age': age,
      'isOnboardingComplete': isOnboardingComplete,
    };
  }
}
