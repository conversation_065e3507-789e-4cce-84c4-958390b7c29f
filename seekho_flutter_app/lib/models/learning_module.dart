class LearningModule {
  final String id;
  final String title;
  final String description;
  final String? thumbnail;
  final String? icon;
  final String color;
  final int classLevel;
  final String subject;
  final String difficulty;
  final bool isPremium;
  final bool isActive;
  final int order;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String packageId;
  final ModuleMetadata? metadata;

  LearningModule({
    required this.id,
    required this.title,
    required this.description,
    this.thumbnail,
    this.icon,
    required this.color,
    required this.classLevel,
    required this.subject,
    required this.difficulty,
    this.isPremium = false,
    this.isActive = true,
    this.order = 0,
    required this.createdAt,
    required this.updatedAt,
    required this.packageId,
    this.metadata,
  });

  factory LearningModule.fromJson(Map<String, dynamic> json) {
    return LearningModule(
      id: json['_id'] ?? json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      thumbnail: json['thumbnail'],
      icon: json['icon'],
      color: json['color'] ?? '#007bff',
      classLevel: json['class'] ?? json['classLevel'] ?? 1,
      subject: json['subject'] ?? '',
      difficulty: json['difficulty'] ?? 'beginner',
      isPremium: json['isPremium'] ?? false,
      isActive: json['isActive'] ?? true,
      order: json['order'] ?? 0,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      packageId: json['packageId'] ?? '',
      metadata: json['metadata'] != null ? ModuleMetadata.fromJson(json['metadata']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'thumbnail': thumbnail,
      'icon': icon,
      'color': color,
      'class': classLevel,
      'subject': subject,
      'difficulty': difficulty,
      'isPremium': isPremium,
      'isActive': isActive,
      'order': order,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'packageId': packageId,
      'metadata': metadata?.toJson(),
    };
  }
}

class ModuleMetadata {
  final int totalLessons;
  final int totalVideos;
  final int totalDuration;
  final int completedLessons;
  final double progress;

  ModuleMetadata({
    this.totalLessons = 0,
    this.totalVideos = 0,
    this.totalDuration = 0,
    this.completedLessons = 0,
    this.progress = 0.0,
  });

  factory ModuleMetadata.fromJson(Map<String, dynamic> json) {
    return ModuleMetadata(
      totalLessons: json['totalLessons'] ?? 0,
      totalVideos: json['totalVideos'] ?? 0,
      totalDuration: json['totalDuration'] ?? 0,
      completedLessons: json['completedLessons'] ?? 0,
      progress: (json['progress'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalLessons': totalLessons,
      'totalVideos': totalVideos,
      'totalDuration': totalDuration,
      'completedLessons': completedLessons,
      'progress': progress,
    };
  }
}

class LearningModulesResponse {
  final bool success;
  final String? message;
  final int? count;
  final List<LearningModule>? data;
  final Map<String, dynamic>? filters;

  LearningModulesResponse({
    required this.success,
    this.message,
    this.count,
    this.data,
    this.filters,
  });

  factory LearningModulesResponse.fromJson(Map<String, dynamic> json) {
    return LearningModulesResponse(
      success: json['success'] ?? false,
      message: json['message'],
      count: json['count'],
      data: json['data'] != null
          ? (json['data'] as List).map((item) => LearningModule.fromJson(item)).toList()
          : null,
      filters: json['filters'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'count': count,
      'data': data?.map((module) => module.toJson()).toList(),
      'filters': filters,
    };
  }
}
