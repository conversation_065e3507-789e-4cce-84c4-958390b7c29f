import 'package:flutter/material.dart';

enum ContentType {
  video,
  text,
  mcq,
  questionnaire,
  module,
}

enum ProgressStatus {
  notStarted,
  inProgress,
  completed,
}

class ContentProgress {
  final String contentId;
  final ContentType contentType;
  final ProgressStatus status;
  final double progressPercentage;
  final int timeSpent; // in seconds
  final DateTime lastAccessed;
  final Map<String, dynamic> metadata;

  ContentProgress({
    required this.contentId,
    required this.contentType,
    required this.status,
    required this.progressPercentage,
    required this.timeSpent,
    required this.lastAccessed,
    this.metadata = const {},
  });

  factory ContentProgress.fromJson(Map<String, dynamic> json) {
    return ContentProgress(
      contentId: json['contentId'] ?? '',
      contentType: ContentType.values.firstWhere(
        (e) => e.toString().split('.').last == json['contentType'],
        orElse: () => ContentType.text,
      ),
      status: ProgressStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => ProgressStatus.notStarted,
      ),
      progressPercentage: (json['progressPercentage'] ?? 0.0).toDouble(),
      timeSpent: json['timeSpent'] ?? 0,
      lastAccessed: DateTime.parse(json['lastAccessed'] ?? DateTime.now().toIso8601String()),
      metadata: json['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'contentId': contentId,
      'contentType': contentType.toString().split('.').last,
      'status': status.toString().split('.').last,
      'progressPercentage': progressPercentage,
      'timeSpent': timeSpent,
      'lastAccessed': lastAccessed.toIso8601String(),
      'metadata': metadata,
    };
  }

  ContentProgress copyWith({
    String? contentId,
    ContentType? contentType,
    ProgressStatus? status,
    double? progressPercentage,
    int? timeSpent,
    DateTime? lastAccessed,
    Map<String, dynamic>? metadata,
  }) {
    return ContentProgress(
      contentId: contentId ?? this.contentId,
      contentType: contentType ?? this.contentType,
      status: status ?? this.status,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      timeSpent: timeSpent ?? this.timeSpent,
      lastAccessed: lastAccessed ?? this.lastAccessed,
      metadata: metadata ?? this.metadata,
    );
  }
}

class ModuleProgress {
  final String moduleId;
  final List<ContentProgress> contentProgress;
  final double overallProgress;
  final ProgressStatus status;
  final DateTime lastAccessed;

  ModuleProgress({
    required this.moduleId,
    required this.contentProgress,
    required this.overallProgress,
    required this.status,
    required this.lastAccessed,
  });

  factory ModuleProgress.fromJson(Map<String, dynamic> json) {
    return ModuleProgress(
      moduleId: json['moduleId'] ?? '',
      contentProgress: (json['contentProgress'] as List<dynamic>?)
              ?.map((item) => ContentProgress.fromJson(item))
              .toList() ??
          [],
      overallProgress: (json['overallProgress'] ?? 0.0).toDouble(),
      status: ProgressStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => ProgressStatus.notStarted,
      ),
      lastAccessed: DateTime.parse(json['lastAccessed'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'moduleId': moduleId,
      'contentProgress': contentProgress.map((cp) => cp.toJson()).toList(),
      'overallProgress': overallProgress,
      'status': status.toString().split('.').last,
      'lastAccessed': lastAccessed.toIso8601String(),
    };
  }
}

/// Utility class for progress-related UI helpers
class ProgressUtils {
  /// Get progress color
  static Color getProgressColor(ProgressStatus status) {
    switch (status) {
      case ProgressStatus.completed:
        return const Color(0xFF4CAF50); // Green
      case ProgressStatus.inProgress:
        return const Color(0xFF6C5CE7); // Purple
      case ProgressStatus.notStarted:
        return Colors.grey;
    }
  }

  /// Get progress icon
  static IconData getProgressIcon(ProgressStatus status) {
    switch (status) {
      case ProgressStatus.completed:
        return Icons.check_circle;
      case ProgressStatus.inProgress:
        return Icons.play_circle;
      case ProgressStatus.notStarted:
        return Icons.radio_button_unchecked;
    }
  }

  /// Format time spent
  static String formatTimeSpent(int seconds) {
    if (seconds < 60) {
      return '${seconds}s';
    } else if (seconds < 3600) {
      final minutes = seconds ~/ 60;
      return '${minutes}m';
    } else {
      final hours = seconds ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;
      return '${hours}h ${minutes}m';
    }
  }

  /// Determine status based on progress percentage
  static ProgressStatus determineStatus(double progressPercentage) {
    if (progressPercentage >= 100.0) {
      return ProgressStatus.completed;
    } else if (progressPercentage > 0.0) {
      return ProgressStatus.inProgress;
    } else {
      return ProgressStatus.notStarted;
    }
  }
}
