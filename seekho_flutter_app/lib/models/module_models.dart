class ContentData {
  final String id;
  final String title;
  final int? estimatedTime;
  final bool isPremium;
  final String slug;
  final Map<String, dynamic>? metadata;
  final Map<String, dynamic> rawData;

  ContentData({
    required this.id,
    required this.title,
    this.estimatedTime,
    required this.isPremium,
    required this.slug,
    this.metadata,
    required this.rawData,
  });

  factory ContentData.fromJson(Map<String, dynamic> json) {
    return ContentData(
      id: json['id'] ?? json['_id'] ?? '',
      title: json['title'] ?? '',
      estimatedTime: json['estimatedTime'],
      isPremium: json['isPremium'] ?? false,
      slug: json['slug'] ?? '',
      metadata: json['metadata'],
      rawData: json,
    );
  }
}

class ModuleContent {
  final String contentType;
  final String contentId;
  final String contentModel;
  final int order;
  final bool isRequired;
  final String? unlockAfter;
  final String? customTitle;
  final String? customDescription;
  final String id;
  final ContentData? contentData;

  ModuleContent({
    required this.contentType,
    required this.contentId,
    required this.contentModel,
    required this.order,
    required this.isRequired,
    this.unlockAfter,
    this.customTitle,
    this.customDescription,
    required this.id,
    this.contentData,
  });

  factory ModuleContent.fromJson(Map<String, dynamic> json) {
    return ModuleContent(
      contentType: json['contentType'] ?? '',
      contentId: json['contentId'] ?? '',
      contentModel: json['contentModel'] ?? '',
      order: json['order'] ?? 0,
      isRequired: json['isRequired'] ?? false,
      unlockAfter: json['unlockAfter'],
      customTitle: json['customTitle'],
      customDescription: json['customDescription'],
      id: json['id'] ?? json['_id'] ?? '',
      contentData: json['contentData'] != null
          ? ContentData.fromJson(json['contentData'])
          : null,
    );
  }
}

class ModuleTopic {
  final String id;
  final String title;
  final String slug;

  ModuleTopic({
    required this.id,
    required this.title,
    required this.slug,
  });

  factory ModuleTopic.fromJson(Map<String, dynamic> json) {
    return ModuleTopic(
      id: json['id'] ?? json['_id'] ?? '',
      title: json['title'] ?? '',
      slug: json['slug'] ?? '',
    );
  }
}

class ModuleMetadata {
  final int totalContent;
  final int totalVideos;
  final int totalQuestionnaires;
  final int totalMCQs;
  final int totalTextContent;
  final int totalEnrollments;
  final double averageCompletionRate;
  final double averageRating;

  ModuleMetadata({
    required this.totalContent,
    required this.totalVideos,
    required this.totalQuestionnaires,
    required this.totalMCQs,
    required this.totalTextContent,
    required this.totalEnrollments,
    required this.averageCompletionRate,
    required this.averageRating,
  });

  factory ModuleMetadata.fromJson(Map<String, dynamic> json) {
    return ModuleMetadata(
      totalContent: json['totalContent'] ?? 0,
      totalVideos: json['totalVideos'] ?? 0,
      totalQuestionnaires: json['totalQuestionnaires'] ?? 0,
      totalMCQs: json['totalMCQs'] ?? 0,
      totalTextContent: json['totalTextContent'] ?? 0,
      totalEnrollments: json['totalEnrollments'] ?? 0,
      averageCompletionRate: (json['averageCompletionRate'] ?? 0).toDouble(),
      averageRating: (json['averageRating'] ?? 0).toDouble(),
    );
  }
}

class LearningModule {
  final String id;
  final String packageId;
  final String title;
  final String description;
  final ModuleTopic topic;
  final List<ModuleContent> content;
  final String difficulty;
  final int? classNumber;
  final int estimatedDuration;
  final List<String> prerequisites;
  final bool isActive;
  final bool isPremium;
  final int order;
  final List<String> tags;
  final String? thumbnail;
  final String slug;
  final ModuleMetadata metadata;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool? hasAccess;
  final List<ModuleContent>? populatedContent;

  LearningModule({
    required this.id,
    required this.packageId,
    required this.title,
    required this.description,
    required this.topic,
    required this.content,
    required this.difficulty,
    this.classNumber,
    required this.estimatedDuration,
    required this.prerequisites,
    required this.isActive,
    required this.isPremium,
    required this.order,
    required this.tags,
    this.thumbnail,
    required this.slug,
    required this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.hasAccess,
    this.populatedContent,
  });

  factory LearningModule.fromJson(Map<String, dynamic> json) {
    return LearningModule(
      id: json['id'] ?? json['_id'] ?? '',
      packageId: json['packageId'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      topic: ModuleTopic.fromJson(json['topic'] ?? {}),
      content: (json['content'] as List<dynamic>?)
              ?.map((item) => ModuleContent.fromJson(item))
              .toList() ??
          [],
      difficulty: json['difficulty'] ?? '',
      classNumber: json['classNumber'],
      estimatedDuration: json['estimatedDuration'] ?? 0,
      prerequisites: List<String>.from(json['prerequisites'] ?? []),
      isActive: json['isActive'] ?? false,
      isPremium: json['isPremium'] ?? false,
      order: json['order'] ?? 0,
      tags: List<String>.from(json['tags'] ?? []),
      thumbnail: json['thumbnail'],
      slug: json['slug'] ?? '',
      metadata: ModuleMetadata.fromJson(json['metadata'] ?? {}),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      hasAccess: json['hasAccess'],
      populatedContent: (json['populatedContent'] as List<dynamic>?)
              ?.map((item) => ModuleContent.fromJson(item))
              .toList(),
    );
  }

  // Helper methods to get content type counts
  int get videoCount => metadata.totalVideos;
  int get textCount => metadata.totalTextContent;
  int get quizCount => metadata.totalQuestionnaires + metadata.totalMCQs;
  int get totalContentCount => metadata.totalContent;

  // Helper method to get primary content type for display
  String get primaryContentType {
    if (videoCount > 0) return 'video';
    if (quizCount > 0) return 'quiz';
    if (textCount > 0) return 'text';
    return 'lesson';
  }

  // Helper method to get content type icon
  String get contentTypeIcon {
    switch (primaryContentType) {
      case 'video':
        return '🎥';
      case 'quiz':
        return '📝';
      case 'text':
        return '📖';
      default:
        return '📚';
    }
  }
}

class ModulesResponse {
  final bool success;
  final int count;
  final int? total;
  final List<LearningModule> data;
  final Map<String, dynamic>? filters;
  final Map<String, dynamic>? pagination;

  ModulesResponse({
    required this.success,
    required this.count,
    this.total,
    required this.data,
    this.filters,
    this.pagination,
  });

  factory ModulesResponse.fromJson(Map<String, dynamic> json) {
    return ModulesResponse(
      success: json['success'] ?? false,
      count: json['count'] ?? 0,
      total: json['total'],
      data: (json['data'] as List<dynamic>?)
              ?.map((item) => LearningModule.fromJson(item))
              .toList() ??
          [],
      filters: json['filters'],
      pagination: json['pagination'],
    );
  }
}
