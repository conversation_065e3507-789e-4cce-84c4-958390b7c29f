import 'dart:convert';

/// Enhanced user statistics request model for activity updates
class UserStatsUpdateRequest {
  final String activityType;
  final String? contentId;
  final String? contentType;
  final int? timeSpent;
  final double? score;

  UserStatsUpdateRequest({
    required this.activityType,
    this.contentId,
    this.contentType,
    this.timeSpent,
    this.score,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'activityType': activityType,
    };
    
    if (contentId != null) data['contentId'] = contentId;
    if (contentType != null) data['contentType'] = contentType;
    if (timeSpent != null) data['timeSpent'] = timeSpent;
    if (score != null) data['score'] = score;
    
    return data;
  }

  String toJsonString() => json.encode(toJson());
}

/// User statistics update response model
class UserStatsUpdateResponse {
  final bool success;
  final String message;
  final UserStatsUpdateData? data;

  UserStatsUpdateResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory UserStatsUpdateResponse.fromJson(Map<String, dynamic> json) {
    return UserStatsUpdateResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? UserStatsUpdateData.fromJson(json['data']) : null,
    );
  }
}

class UserStatsUpdateData {
  final int currentStreak;
  final String lastActivityAt;
  final List<Achievement> newAchievements;

  UserStatsUpdateData({
    required this.currentStreak,
    required this.lastActivityAt,
    required this.newAchievements,
  });

  factory UserStatsUpdateData.fromJson(Map<String, dynamic> json) {
    final List<Achievement> achievements = [];
    if (json['newAchievements'] != null) {
      final achievementsList = json['newAchievements'] as List;
      achievements.addAll(achievementsList.map((item) => Achievement.fromJson(item)));
    }

    return UserStatsUpdateData(
      currentStreak: json['currentStreak'] ?? 0,
      lastActivityAt: json['lastActivityAt'] ?? '',
      newAchievements: achievements,
    );
  }
}

/// Detailed user statistics response model
class DetailedUserStatsResponse {
  final bool success;
  final DetailedUserStats? data;

  DetailedUserStatsResponse({
    required this.success,
    this.data,
  });

  factory DetailedUserStatsResponse.fromJson(Map<String, dynamic> json) {
    return DetailedUserStatsResponse(
      success: json['success'] ?? false,
      data: json['data'] != null ? DetailedUserStats.fromJson(json['data']) : null,
    );
  }
}

class DetailedUserStats {
  final int videosWatched;
  final int totalWatchTime;
  final int completedCourses;
  final int favoriteVideos;
  final int totalBookmarks;
  final int currentStreak;
  final double averageProgress;
  final Map<String, ModuleProgressStats> progressByModule;
  final List<RecentActivity> recentActivity;
  final List<Achievement> achievements;

  DetailedUserStats({
    required this.videosWatched,
    required this.totalWatchTime,
    required this.completedCourses,
    required this.favoriteVideos,
    required this.totalBookmarks,
    required this.currentStreak,
    required this.averageProgress,
    required this.progressByModule,
    required this.recentActivity,
    required this.achievements,
  });

  factory DetailedUserStats.fromJson(Map<String, dynamic> json) {
    final Map<String, ModuleProgressStats> moduleProgress = {};
    if (json['progressByModule'] != null) {
      final moduleMap = json['progressByModule'] as Map<String, dynamic>;
      moduleMap.forEach((key, value) {
        moduleProgress[key] = ModuleProgressStats.fromJson(value);
      });
    }

    final List<RecentActivity> activities = [];
    if (json['recentActivity'] != null) {
      final activitiesList = json['recentActivity'] as List;
      activities.addAll(activitiesList.map((item) => RecentActivity.fromJson(item)));
    }

    final List<Achievement> achievements = [];
    if (json['achievements'] != null) {
      final achievementsList = json['achievements'] as List;
      achievements.addAll(achievementsList.map((item) => Achievement.fromJson(item)));
    }

    return DetailedUserStats(
      videosWatched: json['videosWatched'] ?? 0,
      totalWatchTime: json['totalWatchTime'] ?? 0,
      completedCourses: json['completedCourses'] ?? 0,
      favoriteVideos: json['favoriteVideos'] ?? 0,
      totalBookmarks: json['totalBookmarks'] ?? 0,
      currentStreak: json['currentStreak'] ?? 0,
      averageProgress: (json['averageProgress'] ?? 0.0).toDouble(),
      progressByModule: moduleProgress,
      recentActivity: activities,
      achievements: achievements,
    );
  }
}

class ModuleProgressStats {
  final int completedContent;
  final int totalContent;
  final double progressPercentage;

  ModuleProgressStats({
    required this.completedContent,
    required this.totalContent,
    required this.progressPercentage,
  });

  factory ModuleProgressStats.fromJson(Map<String, dynamic> json) {
    return ModuleProgressStats(
      completedContent: json['completedContent'] ?? 0,
      totalContent: json['totalContent'] ?? 0,
      progressPercentage: (json['progressPercentage'] ?? 0.0).toDouble(),
    );
  }
}

class RecentActivity {
  final String type;
  final String contentTitle;
  final String timestamp;

  RecentActivity({
    required this.type,
    required this.contentTitle,
    required this.timestamp,
  });

  factory RecentActivity.fromJson(Map<String, dynamic> json) {
    return RecentActivity(
      type: json['type'] ?? '',
      contentTitle: json['contentTitle'] ?? '',
      timestamp: json['timestamp'] ?? '',
    );
  }
}

class Achievement {
  final String id;
  final String title;
  final String? description;
  final String? iconUrl;
  final String unlockedAt;

  Achievement({
    required this.id,
    required this.title,
    this.description,
    this.iconUrl,
    required this.unlockedAt,
  });

  factory Achievement.fromJson(Map<String, dynamic> json) {
    return Achievement(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'],
      iconUrl: json['iconUrl'],
      unlockedAt: json['unlockedAt'] ?? '',
    );
  }
}

/// Activity types for statistics updates
class ActivityType {
  static const String videoWatched = 'video_watched';
  static const String contentCompleted = 'content_completed';
  static const String testPassed = 'test_passed';
  static const String login = 'login';
  static const String moduleCompleted = 'module_completed';
  static const String streakMaintained = 'streak_maintained';
}

/// Helper class for formatting statistics
class StatsFormatter {
  /// Format watch time in a human-readable format
  static String formatWatchTime(int seconds) {
    if (seconds < 60) {
      return '${seconds}s';
    } else if (seconds < 3600) {
      final minutes = seconds ~/ 60;
      return '${minutes}m';
    } else {
      final hours = seconds ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;
      if (minutes > 0) {
        return '${hours}h ${minutes}m';
      } else {
        return '${hours}h';
      }
    }
  }

  /// Format large numbers with K, M suffixes
  static String formatNumber(int number) {
    if (number < 1000) {
      return number.toString();
    } else if (number < 1000000) {
      final k = number / 1000;
      return k % 1 == 0 ? '${k.toInt()}K' : '${k.toStringAsFixed(1)}K';
    } else {
      final m = number / 1000000;
      return m % 1 == 0 ? '${m.toInt()}M' : '${m.toStringAsFixed(1)}M';
    }
  }

  /// Format percentage with proper decimal places
  static String formatPercentage(double percentage) {
    if (percentage % 1 == 0) {
      return '${percentage.toInt()}%';
    } else {
      return '${percentage.toStringAsFixed(1)}%';
    }
  }

  /// Get a friendly description for activity type
  static String getActivityDescription(String activityType) {
    switch (activityType) {
      case ActivityType.videoWatched:
        return 'Watched video';
      case ActivityType.contentCompleted:
        return 'Completed content';
      case ActivityType.testPassed:
        return 'Passed test';
      case ActivityType.login:
        return 'Logged in';
      case ActivityType.moduleCompleted:
        return 'Completed module';
      case ActivityType.streakMaintained:
        return 'Maintained streak';
      default:
        return 'Activity';
    }
  }
}
