import 'dart:convert';

/// Enhanced progress request model for the new API
class EnhancedProgressRequest {
  final String contentId;
  final String contentType;
  final double progressPercentage;
  final int timeSpent;
  final String status;
  final Map<String, dynamic>? metadata;

  EnhancedProgressRequest({
    required this.contentId,
    required this.contentType,
    required this.progressPercentage,
    required this.timeSpent,
    required this.status,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'contentId': contentId,
      'contentType': contentType,
      'progressPercentage': progressPercentage,
      'timeSpent': timeSpent,
      'status': status,
      if (metadata != null) 'metadata': metadata,
    };
  }

  String toJsonString() => json.encode(toJson());
}

/// Enhanced progress response model
class EnhancedProgressResponse {
  final bool success;
  final String message;
  final EnhancedProgressData? data;

  EnhancedProgressResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory EnhancedProgressResponse.fromJson(Map<String, dynamic> json) {
    return EnhancedProgressResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? EnhancedProgressData.fromJson(json['data']) : null,
    );
  }
}

class EnhancedProgressData {
  final String progressId;
  final String updatedAt;

  EnhancedProgressData({
    required this.progressId,
    required this.updatedAt,
  });

  factory EnhancedProgressData.fromJson(Map<String, dynamic> json) {
    return EnhancedProgressData(
      progressId: json['progressId'] ?? '',
      updatedAt: json['updatedAt'] ?? '',
    );
  }
}

/// Bulk progress response model
class BulkProgressResponse {
  final bool success;
  final Map<String, BulkProgressItem> data;

  BulkProgressResponse({
    required this.success,
    required this.data,
  });

  factory BulkProgressResponse.fromJson(Map<String, dynamic> json) {
    final Map<String, BulkProgressItem> progressData = {};
    
    if (json['data'] != null) {
      final dataMap = json['data'] as Map<String, dynamic>;
      dataMap.forEach((key, value) {
        progressData[key] = BulkProgressItem.fromJson(value);
      });
    }

    return BulkProgressResponse(
      success: json['success'] ?? false,
      data: progressData,
    );
  }
}

class BulkProgressItem {
  final double progressPercentage;
  final String status;
  final int timeSpent;
  final String lastAccessed;
  final Map<String, dynamic> metadata;

  BulkProgressItem({
    required this.progressPercentage,
    required this.status,
    required this.timeSpent,
    required this.lastAccessed,
    required this.metadata,
  });

  factory BulkProgressItem.fromJson(Map<String, dynamic> json) {
    return BulkProgressItem(
      progressPercentage: (json['progressPercentage'] ?? 0.0).toDouble(),
      status: json['status'] ?? 'notStarted',
      timeSpent: json['timeSpent'] ?? 0,
      lastAccessed: json['lastAccessed'] ?? '',
      metadata: json['metadata'] ?? {},
    );
  }
}

/// User progress summary response model
class UserProgressSummaryResponse {
  final bool success;
  final UserProgressSummary? data;

  UserProgressSummaryResponse({
    required this.success,
    this.data,
  });

  factory UserProgressSummaryResponse.fromJson(Map<String, dynamic> json) {
    return UserProgressSummaryResponse(
      success: json['success'] ?? false,
      data: json['data'] != null ? UserProgressSummary.fromJson(json['data']) : null,
    );
  }
}

class UserProgressSummary {
  final int totalContent;
  final int completedContent;
  final int inProgressContent;
  final double overallProgress;
  final int totalTimeSpent;
  final Map<String, ModuleProgress> moduleProgress;

  UserProgressSummary({
    required this.totalContent,
    required this.completedContent,
    required this.inProgressContent,
    required this.overallProgress,
    required this.totalTimeSpent,
    required this.moduleProgress,
  });

  factory UserProgressSummary.fromJson(Map<String, dynamic> json) {
    final Map<String, ModuleProgress> modules = {};
    
    if (json['moduleProgress'] != null) {
      final moduleMap = json['moduleProgress'] as Map<String, dynamic>;
      moduleMap.forEach((key, value) {
        modules[key] = ModuleProgress.fromJson(value);
      });
    }

    return UserProgressSummary(
      totalContent: json['totalContent'] ?? 0,
      completedContent: json['completedContent'] ?? 0,
      inProgressContent: json['inProgressContent'] ?? 0,
      overallProgress: (json['overallProgress'] ?? 0.0).toDouble(),
      totalTimeSpent: json['totalTimeSpent'] ?? 0,
      moduleProgress: modules,
    );
  }
}

class ModuleProgress {
  final int completedContent;
  final int totalContent;
  final double progressPercentage;

  ModuleProgress({
    required this.completedContent,
    required this.totalContent,
    required this.progressPercentage,
  });

  factory ModuleProgress.fromJson(Map<String, dynamic> json) {
    return ModuleProgress(
      completedContent: json['completedContent'] ?? 0,
      totalContent: json['totalContent'] ?? 0,
      progressPercentage: (json['progressPercentage'] ?? 0.0).toDouble(),
    );
  }
}

/// Module-specific progress response model
class ModuleProgressResponse {
  final bool success;
  final ModuleProgressData? data;

  ModuleProgressResponse({
    required this.success,
    this.data,
  });

  factory ModuleProgressResponse.fromJson(Map<String, dynamic> json) {
    return ModuleProgressResponse(
      success: json['success'] ?? false,
      data: json['data'] != null ? ModuleProgressData.fromJson(json['data']) : null,
    );
  }
}

class ModuleProgressData {
  final String moduleId;
  final String moduleTitle;
  final int completedContent;
  final int totalContent;
  final double progressPercentage;
  final List<ContentProgressItem> contentProgress;

  ModuleProgressData({
    required this.moduleId,
    required this.moduleTitle,
    required this.completedContent,
    required this.totalContent,
    required this.progressPercentage,
    required this.contentProgress,
  });

  factory ModuleProgressData.fromJson(Map<String, dynamic> json) {
    final List<ContentProgressItem> content = [];
    
    if (json['contentProgress'] != null) {
      final contentList = json['contentProgress'] as List;
      content.addAll(contentList.map((item) => ContentProgressItem.fromJson(item)));
    }

    return ModuleProgressData(
      moduleId: json['moduleId'] ?? '',
      moduleTitle: json['moduleTitle'] ?? '',
      completedContent: json['completedContent'] ?? 0,
      totalContent: json['totalContent'] ?? 0,
      progressPercentage: (json['progressPercentage'] ?? 0.0).toDouble(),
      contentProgress: content,
    );
  }
}

class ContentProgressItem {
  final String contentId;
  final String contentType;
  final String contentTitle;
  final double progressPercentage;
  final String status;
  final int timeSpent;
  final String lastAccessed;

  ContentProgressItem({
    required this.contentId,
    required this.contentType,
    required this.contentTitle,
    required this.progressPercentage,
    required this.status,
    required this.timeSpent,
    required this.lastAccessed,
  });

  factory ContentProgressItem.fromJson(Map<String, dynamic> json) {
    return ContentProgressItem(
      contentId: json['contentId'] ?? '',
      contentType: json['contentType'] ?? '',
      contentTitle: json['contentTitle'] ?? '',
      progressPercentage: (json['progressPercentage'] ?? 0.0).toDouble(),
      status: json['status'] ?? 'notStarted',
      timeSpent: json['timeSpent'] ?? 0,
      lastAccessed: json['lastAccessed'] ?? '',
    );
  }
}
