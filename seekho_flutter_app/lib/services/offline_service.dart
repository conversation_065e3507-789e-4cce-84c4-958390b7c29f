import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/module_models.dart';
import '../models/mcq_models.dart';
import '../services/text_content_service.dart';
import '../services/modules_service.dart';
import '../services/mcq_service.dart';

enum CacheStatus {
  notCached,
  caching,
  cached,
  failed,
}

class CachedContent {
  final String contentId;
  final String contentType;
  final String title;
  final DateTime cachedAt;
  final int sizeInBytes;
  final String filePath;
  final Map<String, dynamic> metadata;

  CachedContent({
    required this.contentId,
    required this.contentType,
    required this.title,
    required this.cachedAt,
    required this.sizeInBytes,
    required this.filePath,
    this.metadata = const {},
  });

  factory CachedContent.fromJson(Map<String, dynamic> json) {
    return CachedContent(
      contentId: json['contentId'] ?? '',
      contentType: json['contentType'] ?? '',
      title: json['title'] ?? '',
      cachedAt: DateTime.parse(json['cachedAt'] ?? DateTime.now().toIso8601String()),
      sizeInBytes: json['sizeInBytes'] ?? 0,
      filePath: json['filePath'] ?? '',
      metadata: json['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'contentId': contentId,
      'contentType': contentType,
      'title': title,
      'cachedAt': cachedAt.toIso8601String(),
      'sizeInBytes': sizeInBytes,
      'filePath': filePath,
      'metadata': metadata,
    };
  }
}

class OfflineService {
  static const String _cacheIndexKey = 'offline_cache_index';
  static const String _cacheDirectoryName = 'offline_content';
  static const int _maxCacheSizeBytes = 500 * 1024 * 1024; // 500MB
  static const int _maxCacheAgeHours = 24 * 7; // 1 week

  /// Get cache directory
  static Future<Directory> _getCacheDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final cacheDir = Directory('${appDir.path}/$_cacheDirectoryName');
    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }
    return cacheDir;
  }

  /// Get cached content index
  static Future<Map<String, CachedContent>> _getCacheIndex() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final indexData = prefs.getString(_cacheIndexKey);
      
      if (indexData != null) {
        final indexMap = json.decode(indexData) as Map<String, dynamic>;
        return indexMap.map((key, value) => 
          MapEntry(key, CachedContent.fromJson(value))
        );
      }
      return {};
    } catch (e) {
      print('Error getting cache index: $e');
      return {};
    }
  }

  /// Save cached content index
  static Future<void> _saveCacheIndex(Map<String, CachedContent> index) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final indexMap = index.map((key, value) => 
        MapEntry(key, value.toJson())
      );
      await prefs.setString(_cacheIndexKey, json.encode(indexMap));
    } catch (e) {
      print('Error saving cache index: $e');
    }
  }

  /// Check if content is cached
  static Future<CacheStatus> getCacheStatus(String contentId) async {
    try {
      final index = await _getCacheIndex();
      final cachedContent = index[contentId];
      
      if (cachedContent == null) {
        return CacheStatus.notCached;
      }

      // Check if file still exists
      final file = File(cachedContent.filePath);
      if (!await file.exists()) {
        // Remove from index if file doesn't exist
        index.remove(contentId);
        await _saveCacheIndex(index);
        return CacheStatus.notCached;
      }

      // Check if cache is expired
      final ageHours = DateTime.now().difference(cachedContent.cachedAt).inHours;
      if (ageHours > _maxCacheAgeHours) {
        await _removeCachedContent(contentId);
        return CacheStatus.notCached;
      }

      return CacheStatus.cached;
    } catch (e) {
      print('Error checking cache status: $e');
      return CacheStatus.failed;
    }
  }

  /// Cache module content
  static Future<bool> cacheModule(LearningModule module) async {
    try {
      // Get module with populated content
      final moduleWithContent = await ModulesService.getModuleWithContent(module.id);
      if (moduleWithContent?.populatedContent == null) {
        return false;
      }

      bool allCached = true;
      for (final content in moduleWithContent!.populatedContent!) {
        final success = await _cacheContent(content);
        if (!success) {
          allCached = false;
        }
      }

      return allCached;
    } catch (e) {
      print('Error caching module: $e');
      return false;
    }
  }

  /// Cache individual content
  static Future<bool> _cacheContent(ModuleContent content) async {
    try {
      final cacheDir = await _getCacheDirectory();
      final fileName = '${content.contentId}.json';
      final filePath = '${cacheDir.path}/$fileName';
      
      Map<String, dynamic> contentData = {};
      
      switch (content.contentType.toLowerCase()) {
        case 'text':
        case 'summary':
        case 'reading':
        case 'instructions':
        case 'notes':
          final textContent = await TextContentService.getTextContentById(content.contentId);
          if (textContent != null) {
            contentData = {
              'type': 'text',
              'data': textContent.toJson(),
            };
          }
          break;
          
        case 'mcq':
          final mcqContent = await MCQService.getMCQById(content.contentId);
          if (mcqContent != null) {
            contentData = {
              'type': 'mcq',
              'data': mcqContent.toJson(),
            };
          }
          break;
          
        case 'video':
          // For videos, we'll cache metadata but not the actual video file
          contentData = {
            'type': 'video',
            'data': content.contentData?.rawData ?? {},
          };
          break;
          
        default:
          contentData = {
            'type': 'unknown',
            'data': content.contentData?.rawData ?? {},
          };
      }

      if (contentData.isEmpty) {
        return false;
      }

      // Write content to file
      final file = File(filePath);
      final jsonString = json.encode(contentData);
      await file.writeAsString(jsonString);

      // Update cache index
      final index = await _getCacheIndex();
      index[content.contentId] = CachedContent(
        contentId: content.contentId,
        contentType: content.contentType,
        title: content.contentData?.title ?? 'Cached Content',
        cachedAt: DateTime.now(),
        sizeInBytes: jsonString.length,
        filePath: filePath,
        metadata: {
          'moduleId': content.contentData?.rawData['moduleId'],
          'order': content.order,
        },
      );
      
      await _saveCacheIndex(index);
      await _cleanupOldCache();
      
      return true;
    } catch (e) {
      print('Error caching content ${content.contentId}: $e');
      return false;
    }
  }

  /// Get cached content
  static Future<Map<String, dynamic>?> getCachedContent(String contentId) async {
    try {
      final status = await getCacheStatus(contentId);
      if (status != CacheStatus.cached) {
        return null;
      }

      final index = await _getCacheIndex();
      final cachedContent = index[contentId];
      if (cachedContent == null) {
        return null;
      }

      final file = File(cachedContent.filePath);
      if (!await file.exists()) {
        return null;
      }

      final jsonString = await file.readAsString();
      return json.decode(jsonString);
    } catch (e) {
      print('Error getting cached content: $e');
      return null;
    }
  }

  /// Remove cached content
  static Future<bool> _removeCachedContent(String contentId) async {
    try {
      final index = await _getCacheIndex();
      final cachedContent = index[contentId];
      
      if (cachedContent != null) {
        final file = File(cachedContent.filePath);
        if (await file.exists()) {
          await file.delete();
        }
        index.remove(contentId);
        await _saveCacheIndex(index);
      }
      
      return true;
    } catch (e) {
      print('Error removing cached content: $e');
      return false;
    }
  }

  /// Get all cached content
  static Future<List<CachedContent>> getAllCachedContent() async {
    try {
      final index = await _getCacheIndex();
      return index.values.toList()
        ..sort((a, b) => b.cachedAt.compareTo(a.cachedAt));
    } catch (e) {
      print('Error getting all cached content: $e');
      return [];
    }
  }

  /// Get cache size
  static Future<int> getCacheSize() async {
    try {
      final index = await _getCacheIndex();
      return index.values.fold<int>(0, (sum, content) => sum + content.sizeInBytes);
    } catch (e) {
      print('Error getting cache size: $e');
      return 0;
    }
  }

  /// Clear all cache
  static Future<bool> clearAllCache() async {
    try {
      final cacheDir = await _getCacheDirectory();
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
      }
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheIndexKey);
      
      return true;
    } catch (e) {
      print('Error clearing cache: $e');
      return false;
    }
  }

  /// Cleanup old cache entries
  static Future<void> _cleanupOldCache() async {
    try {
      final index = await _getCacheIndex();
      final now = DateTime.now();
      final toRemove = <String>[];

      // Remove expired entries
      for (final entry in index.entries) {
        final ageHours = now.difference(entry.value.cachedAt).inHours;
        if (ageHours > _maxCacheAgeHours) {
          toRemove.add(entry.key);
        }
      }

      // Remove entries if cache is too large
      final totalSize = await getCacheSize();
      if (totalSize > _maxCacheSizeBytes) {
        final sortedEntries = index.entries.toList()
          ..sort((a, b) => a.value.cachedAt.compareTo(b.value.cachedAt));
        
        int currentSize = totalSize;
        for (final entry in sortedEntries) {
          if (currentSize <= _maxCacheSizeBytes * 0.8) break; // Keep 80% of max size
          toRemove.add(entry.key);
          currentSize -= entry.value.sizeInBytes;
        }
      }

      // Remove identified entries
      for (final contentId in toRemove) {
        await _removeCachedContent(contentId);
      }
    } catch (e) {
      print('Error cleaning up cache: $e');
    }
  }

  /// Format cache size
  static String formatCacheSize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  /// Check if device is online
  static Future<bool> isOnline() async {
    try {
      final result = await http.get(
        Uri.parse('https://www.google.com'),
        headers: {'Connection': 'close'},
      ).timeout(const Duration(seconds: 5));
      return result.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
