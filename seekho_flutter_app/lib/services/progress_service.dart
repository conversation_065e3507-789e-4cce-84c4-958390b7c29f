import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'auth_service.dart';

enum ContentType {
  video,
  text,
  mcq,
  questionnaire,
  module,
}

enum ProgressStatus {
  notStarted,
  inProgress,
  completed,
}

class ContentProgress {
  final String contentId;
  final ContentType contentType;
  final ProgressStatus status;
  final double progressPercentage;
  final int timeSpent; // in seconds
  final DateTime lastAccessed;
  final Map<String, dynamic> metadata;

  ContentProgress({
    required this.contentId,
    required this.contentType,
    required this.status,
    required this.progressPercentage,
    required this.timeSpent,
    required this.lastAccessed,
    this.metadata = const {},
  });

  factory ContentProgress.fromJson(Map<String, dynamic> json) {
    return ContentProgress(
      contentId: json['contentId'] ?? '',
      contentType: ContentType.values.firstWhere(
        (e) => e.toString().split('.').last == json['contentType'],
        orElse: () => ContentType.text,
      ),
      status: ProgressStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => ProgressStatus.notStarted,
      ),
      progressPercentage: (json['progressPercentage'] ?? 0.0).toDouble(),
      timeSpent: json['timeSpent'] ?? 0,
      lastAccessed: DateTime.parse(json['lastAccessed'] ?? DateTime.now().toIso8601String()),
      metadata: json['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'contentId': contentId,
      'contentType': contentType.toString().split('.').last,
      'status': status.toString().split('.').last,
      'progressPercentage': progressPercentage,
      'timeSpent': timeSpent,
      'lastAccessed': lastAccessed.toIso8601String(),
      'metadata': metadata,
    };
  }

  ContentProgress copyWith({
    String? contentId,
    ContentType? contentType,
    ProgressStatus? status,
    double? progressPercentage,
    int? timeSpent,
    DateTime? lastAccessed,
    Map<String, dynamic>? metadata,
  }) {
    return ContentProgress(
      contentId: contentId ?? this.contentId,
      contentType: contentType ?? this.contentType,
      status: status ?? this.status,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      timeSpent: timeSpent ?? this.timeSpent,
      lastAccessed: lastAccessed ?? this.lastAccessed,
      metadata: metadata ?? this.metadata,
    );
  }
}

class ModuleProgress {
  final String moduleId;
  final List<ContentProgress> contentProgress;
  final double overallProgress;
  final ProgressStatus status;
  final DateTime lastAccessed;

  ModuleProgress({
    required this.moduleId,
    required this.contentProgress,
    required this.overallProgress,
    required this.status,
    required this.lastAccessed,
  });

  factory ModuleProgress.fromJson(Map<String, dynamic> json) {
    return ModuleProgress(
      moduleId: json['moduleId'] ?? '',
      contentProgress: (json['contentProgress'] as List<dynamic>?)
              ?.map((item) => ContentProgress.fromJson(item))
              .toList() ??
          [],
      overallProgress: (json['overallProgress'] ?? 0.0).toDouble(),
      status: ProgressStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => ProgressStatus.notStarted,
      ),
      lastAccessed: DateTime.parse(json['lastAccessed'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'moduleId': moduleId,
      'contentProgress': contentProgress.map((cp) => cp.toJson()).toList(),
      'overallProgress': overallProgress,
      'status': status.toString().split('.').last,
      'lastAccessed': lastAccessed.toIso8601String(),
    };
  }
}

class ProgressService {
  static const String baseUrl = 'https://learner.netaapp.in';
  static const String packageId = 'com.gumbo.english';
  static const String _progressKey = 'user_progress';

  static Map<String, String> get _headers => {
        'Content-Type': 'application/json',
        'X-Package-ID': packageId,
      };

  /// Get authenticated headers with token
  static Future<Map<String, String>> get _authHeaders async {
    final token = await AuthService.getAccessToken();
    final headers = Map<String, String>.from(_headers);
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  /// Record content progress locally
  static Future<void> recordContentProgress({
    required String contentId,
    required ContentType contentType,
    required double progressPercentage,
    required int timeSpent,
    ProgressStatus? status,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressData = prefs.getString(_progressKey);
      
      Map<String, dynamic> allProgress = {};
      if (progressData != null) {
        allProgress = json.decode(progressData);
      }

      // Determine status based on progress
      ProgressStatus finalStatus = status ?? _determineStatus(progressPercentage);

      final contentProgress = ContentProgress(
        contentId: contentId,
        contentType: contentType,
        status: finalStatus,
        progressPercentage: progressPercentage,
        timeSpent: timeSpent,
        lastAccessed: DateTime.now(),
        metadata: metadata ?? {},
      );

      allProgress[contentId] = contentProgress.toJson();
      await prefs.setString(_progressKey, json.encode(allProgress));

      // Also try to sync with backend
      _syncProgressToBackend(contentProgress);
    } catch (e) {
      print('Error recording content progress: $e');
    }
  }

  /// Get content progress
  static Future<ContentProgress?> getContentProgress(String contentId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressData = prefs.getString(_progressKey);
      
      if (progressData != null) {
        final allProgress = json.decode(progressData);
        if (allProgress.containsKey(contentId)) {
          return ContentProgress.fromJson(allProgress[contentId]);
        }
      }
      return null;
    } catch (e) {
      print('Error getting content progress: $e');
      return null;
    }
  }

  /// Get module progress
  static Future<ModuleProgress?> getModuleProgress(String moduleId, List<String> contentIds) async {
    try {
      final contentProgressList = <ContentProgress>[];
      double totalProgress = 0.0;
      DateTime? lastAccessed;

      for (final contentId in contentIds) {
        final progress = await getContentProgress(contentId);
        if (progress != null) {
          contentProgressList.add(progress);
          totalProgress += progress.progressPercentage;
          
          if (lastAccessed == null || progress.lastAccessed.isAfter(lastAccessed)) {
            lastAccessed = progress.lastAccessed;
          }
        }
      }

      if (contentProgressList.isEmpty) return null;

      final overallProgress = totalProgress / contentIds.length;
      final status = _determineStatus(overallProgress);

      return ModuleProgress(
        moduleId: moduleId,
        contentProgress: contentProgressList,
        overallProgress: overallProgress,
        status: status,
        lastAccessed: lastAccessed ?? DateTime.now(),
      );
    } catch (e) {
      print('Error getting module progress: $e');
      return null;
    }
  }

  /// Sync progress to backend
  static Future<void> _syncProgressToBackend(ContentProgress progress) async {
    try {
      final headers = await _authHeaders;
      String endpoint;
      
      switch (progress.contentType) {
        case ContentType.video:
          endpoint = '/api/videos/${progress.contentId}/progress';
          break;
        case ContentType.text:
          endpoint = '/api/text-content/${progress.contentId}/progress';
          break;
        case ContentType.mcq:
          endpoint = '/api/mcqs/${progress.contentId}/progress';
          break;
        case ContentType.questionnaire:
          endpoint = '/api/questionnaires/${progress.contentId}/progress';
          break;
        default:
          return; // No backend sync for unknown types
      }

      final body = {
        'progress': progress.progressPercentage,
        'timeSpent': progress.timeSpent,
        'status': progress.status.toString().split('.').last,
        'metadata': progress.metadata,
      };

      final response = await http.post(
        Uri.parse('$baseUrl$endpoint'),
        headers: headers,
        body: json.encode(body),
      );

      if (response.statusCode == 200) {
        print('Progress synced to backend for ${progress.contentId}');
      }
    } catch (e) {
      print('Error syncing progress to backend: $e');
    }
  }

  /// Determine status based on progress percentage
  static ProgressStatus _determineStatus(double progressPercentage) {
    if (progressPercentage >= 100.0) {
      return ProgressStatus.completed;
    } else if (progressPercentage > 0.0) {
      return ProgressStatus.inProgress;
    } else {
      return ProgressStatus.notStarted;
    }
  }

  /// Get progress color
  static Color getProgressColor(ProgressStatus status) {
    switch (status) {
      case ProgressStatus.completed:
        return const Color(0xFF4CAF50); // Green
      case ProgressStatus.inProgress:
        return const Color(0xFF6C5CE7); // Purple
      case ProgressStatus.notStarted:
        return Colors.grey;
    }
  }

  /// Get progress icon
  static IconData getProgressIcon(ProgressStatus status) {
    switch (status) {
      case ProgressStatus.completed:
        return Icons.check_circle;
      case ProgressStatus.inProgress:
        return Icons.play_circle;
      case ProgressStatus.notStarted:
        return Icons.radio_button_unchecked;
    }
  }

  /// Format time spent
  static String formatTimeSpent(int seconds) {
    if (seconds < 60) {
      return '${seconds}s';
    } else if (seconds < 3600) {
      final minutes = seconds ~/ 60;
      return '${minutes}m';
    } else {
      final hours = seconds ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;
      return '${hours}h ${minutes}m';
    }
  }

  /// Clear all progress (for testing/reset)
  static Future<void> clearAllProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_progressKey);
    } catch (e) {
      print('Error clearing progress: $e');
    }
  }
}
