import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'enhanced_progress_service.dart';
import '../models/content_progress.dart';

class ProgressService {
  static const String baseUrl = 'https://learner.netaapp.in';
  static const String packageId = 'com.gumbo.english';
  static const String _progressKey = 'user_progress';



  /// Record content progress locally
  static Future<void> recordContentProgress({
    required String contentId,
    required ContentType contentType,
    required double progressPercentage,
    required int timeSpent,
    ProgressStatus? status,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressData = prefs.getString(_progressKey);
      
      Map<String, dynamic> allProgress = {};
      if (progressData != null) {
        allProgress = json.decode(progressData);
      }

      // Determine status based on progress
      ProgressStatus finalStatus = status ?? _determineStatus(progressPercentage);

      final contentProgress = ContentProgress(
        contentId: contentId,
        contentType: contentType,
        status: finalStatus,
        progressPercentage: progressPercentage,
        timeSpent: timeSpent,
        lastAccessed: DateTime.now(),
        metadata: metadata ?? {},
      );

      allProgress[contentId] = contentProgress.toJson();
      await prefs.setString(_progressKey, json.encode(allProgress));

      // Also try to sync with backend
      _syncProgressToBackend(contentProgress);
    } catch (e) {
      debugPrint('Error recording content progress: $e');
    }
  }

  /// Get content progress
  static Future<ContentProgress?> getContentProgress(String contentId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressData = prefs.getString(_progressKey);
      
      if (progressData != null) {
        final allProgress = json.decode(progressData);
        if (allProgress.containsKey(contentId)) {
          return ContentProgress.fromJson(allProgress[contentId]);
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error getting content progress: $e');
      return null;
    }
  }

  /// Get module progress
  static Future<ModuleProgress?> getModuleProgress(String moduleId, List<String> contentIds) async {
    try {
      final contentProgressList = <ContentProgress>[];
      double totalProgress = 0.0;
      DateTime? lastAccessed;

      for (final contentId in contentIds) {
        final progress = await getContentProgress(contentId);
        if (progress != null) {
          contentProgressList.add(progress);
          totalProgress += progress.progressPercentage;
          
          if (lastAccessed == null || progress.lastAccessed.isAfter(lastAccessed)) {
            lastAccessed = progress.lastAccessed;
          }
        }
      }

      if (contentProgressList.isEmpty) return null;

      final overallProgress = totalProgress / contentIds.length;
      final status = _determineStatus(overallProgress);

      return ModuleProgress(
        moduleId: moduleId,
        contentProgress: contentProgressList,
        overallProgress: overallProgress,
        status: status,
        lastAccessed: lastAccessed ?? DateTime.now(),
      );
    } catch (e) {
      debugPrint('Error getting module progress: $e');
      return null;
    }
  }

  /// Sync progress to backend using enhanced API
  static Future<void> _syncProgressToBackend(ContentProgress progress) async {
    try {
      // Use the enhanced progress service for backend sync
      final success = await EnhancedProgressService.recordProgress(
        contentId: progress.contentId,
        contentType: progress.contentType,
        progressPercentage: progress.progressPercentage,
        timeSpent: progress.timeSpent,
        status: progress.status,
        metadata: progress.metadata,
      );

      if (success) {
        debugPrint('✅ Progress synced to backend for ${progress.contentId}');
      } else {
        debugPrint('❌ Failed to sync progress to backend for ${progress.contentId}');
      }
    } catch (e) {
      debugPrint('❌ Error syncing progress to backend: $e');
    }
  }

  /// Determine status based on progress percentage
  static ProgressStatus _determineStatus(double progressPercentage) {
    if (progressPercentage >= 100.0) {
      return ProgressStatus.completed;
    } else if (progressPercentage > 0.0) {
      return ProgressStatus.inProgress;
    } else {
      return ProgressStatus.notStarted;
    }
  }

  /// Get progress color
  static Color getProgressColor(ProgressStatus status) {
    switch (status) {
      case ProgressStatus.completed:
        return const Color(0xFF4CAF50); // Green
      case ProgressStatus.inProgress:
        return const Color(0xFF6C5CE7); // Purple
      case ProgressStatus.notStarted:
        return Colors.grey;
    }
  }

  /// Get progress icon
  static IconData getProgressIcon(ProgressStatus status) {
    switch (status) {
      case ProgressStatus.completed:
        return Icons.check_circle;
      case ProgressStatus.inProgress:
        return Icons.play_circle;
      case ProgressStatus.notStarted:
        return Icons.radio_button_unchecked;
    }
  }

  /// Format time spent
  static String formatTimeSpent(int seconds) {
    if (seconds < 60) {
      return '${seconds}s';
    } else if (seconds < 3600) {
      final minutes = seconds ~/ 60;
      return '${minutes}m';
    } else {
      final hours = seconds ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;
      return '${hours}h ${minutes}m';
    }
  }

  /// Clear all progress (for testing/reset)
  static Future<void> clearAllProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_progressKey);
    } catch (e) {
      debugPrint('Error clearing progress: $e');
    }
  }
}
