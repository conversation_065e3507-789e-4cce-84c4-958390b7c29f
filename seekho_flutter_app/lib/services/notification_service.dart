import 'dart:convert';
import '../models/notification.dart';
import '../utils/constants.dart';
import 'auth_service.dart';
import 'http_client_service.dart';

class NotificationService {
  // Get user notifications
  static Future<NotificationsResponse> getNotifications({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        return NotificationsResponse(
          success: false,
          message: 'User not authenticated',
        );
      }

      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.notifications}?page=$page&limit=$limit'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      return NotificationsResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error fetching notifications: $e');
      return NotificationsResponse(
        success: false,
        message: 'Failed to fetch notifications: $e',
      );
    }
  }

  // Mark notifications as read
  static Future<bool> markNotificationsAsRead(List<String> notificationIds) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) return false;

      final response = await HttpClientService.post(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.notificationsMarkRead}'),
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: json.encode({'notificationIds': notificationIds}),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error marking notifications as read: $e');
      return false;
    }
  }

  // Get unread notification count
  static Future<UnreadCountResponse> getUnreadCount() async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        return UnreadCountResponse(
          success: false,
          message: 'User not authenticated',
          count: 0,
        );
      }

      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.notificationsUnreadCount}'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      return UnreadCountResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error fetching unread count: $e');
      return UnreadCountResponse(
        success: false,
        message: 'Failed to fetch unread count: $e',
        count: 0,
      );
    }
  }

  // Mark all notifications as read
  static Future<bool> markAllAsRead() async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) return false;

      final response = await HttpClientService.post(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.notificationsMarkRead}'),
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: json.encode({'notificationIds': []}), // Empty array marks all as read
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error marking all notifications as read: $e');
      return false;
    }
  }
}
