import 'dart:convert';
import '../models/user.dart';
import '../models/user_stats.dart';
import '../models/watch_history.dart';
import '../models/video.dart';
import '../utils/constants.dart';
import 'auth_service.dart';
import 'http_client_service.dart';

class UserService {
  // Get user profile
  static Future<User?> getUserProfile() async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) return null;

      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.userProfile}'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] && data['data'] != null) {
          return User.fromJson(data['data']);
        }
      }
      return null;
    } catch (e) {
      print('Error fetching user profile: $e');
      return null;
    }
  }

  // Update user profile
  static Future<bool> updateUserProfile({
    String? name,
    String? username,
    Map<String, dynamic>? preferences,
  }) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) return false;

      final body = <String, dynamic>{};
      if (name != null) body['name'] = name;
      if (username != null) body['username'] = username;
      if (preferences != null) body['preferences'] = preferences;

      final response = await HttpClientService.put(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.userProfile}'),
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: json.encode(body),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error updating user profile: $e');
      return false;
    }
  }

  // Get user statistics
  static Future<UserStatsResponse> getUserStats() async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        return UserStatsResponse(
          success: false,
          message: 'User not authenticated',
        );
      }

      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.userStats}'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      return UserStatsResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error fetching user stats: $e');
      return UserStatsResponse(
        success: false,
        message: 'Failed to fetch user stats: $e',
      );
    }
  }

  // Get watch history
  static Future<WatchHistoryResponse> getWatchHistory({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        return WatchHistoryResponse(
          success: false,
          message: 'User not authenticated',
        );
      }

      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.userWatchHistory}?page=$page&limit=$limit'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      return WatchHistoryResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error fetching watch history: $e');
      return WatchHistoryResponse(
        success: false,
        message: 'Failed to fetch watch history: $e',
      );
    }
  }

  // Add video to favorites
  static Future<bool> addToFavorites(String videoId) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) return false;

      final response = await HttpClientService.post(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.userFavorites}'),
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: json.encode({'videoId': videoId}),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error adding to favorites: $e');
      return false;
    }
  }

  // Remove video from favorites
  static Future<bool> removeFromFavorites(String videoId) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) return false;

      final response = await HttpClientService.delete(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.userFavorites}/$videoId'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error removing from favorites: $e');
      return false;
    }
  }

  // Get favorite videos
  static Future<VideosResponse> getFavoriteVideos({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        return VideosResponse(
          success: false,
          message: 'User not authenticated',
        );
      }

      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.userFavorites}?page=$page&limit=$limit'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      return VideosResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error fetching favorite videos: $e');
      return VideosResponse(
        success: false,
        message: 'Failed to fetch favorite videos: $e',
      );
    }
  }

  // Add bookmark
  static Future<bool> addBookmark(String videoId, {String? note}) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) return false;

      final body = {'videoId': videoId};
      if (note != null) body['note'] = note;

      final response = await HttpClientService.post(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.userBookmarks}'),
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: json.encode(body),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error adding bookmark: $e');
      return false;
    }
  }

  // Remove bookmark
  static Future<bool> removeBookmark(String videoId) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) return false;

      final response = await HttpClientService.delete(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.userBookmarks}/$videoId'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error removing bookmark: $e');
      return false;
    }
  }

  // Get bookmarked videos
  static Future<VideosResponse> getBookmarkedVideos({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        return VideosResponse(
          success: false,
          message: 'User not authenticated',
        );
      }

      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.userBookmarks}?page=$page&limit=$limit'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      return VideosResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error fetching bookmarked videos: $e');
      return VideosResponse(
        success: false,
        message: 'Failed to fetch bookmarked videos: $e',
      );
    }
  }
}
