// Legacy subscription service that redirects to the new one
export 'new_subscription_service.dart';

// For backward compatibility, create an alias
import 'new_subscription_service.dart';

class SubscriptionService {
  // Redirect all methods to NewSubscriptionService
  static Future<bool> hasAccessToPremiumContent() {
    return NewSubscriptionService.hasAccessToPremiumContent();
  }
  
  static getSubscriptionStatus() {
    return NewSubscriptionService.getSubscriptionStatus();
  }
  
  static getSubscriptionPlans() {
    return NewSubscriptionService.getSubscriptionPlans();
  }
}
