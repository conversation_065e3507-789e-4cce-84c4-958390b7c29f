import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/questionnaire_models.dart';
import 'auth_service.dart';

class QuestionnaireService {
  static const String baseUrl = 'https://learner.netaapp.in';
  static const String packageId = 'com.gumbo.english';

  static Map<String, String> get _headers => {
        'Content-Type': 'application/json',
        'X-Package-ID': packageId,
      };

  /// Get authenticated headers with token
  static Future<Map<String, String>> get _authHeaders async {
    final token = await AuthService.getAccessToken();
    final headers = Map<String, String>.from(_headers);
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  /// Get questionnaire data by ID
  static Future<QuestionnaireData?> getQuestionnaireById(String questionnaireId) async {
    try {
      print('🔍 Fetching questionnaire with ID: $questionnaireId');
      final url = Uri.parse('$baseUrl/api/questionnaires/$questionnaireId');
      final response = await http.get(url, headers: _headers);

      print('📡 Questionnaire API Response Status: ${response.statusCode}');
      print('📡 Questionnaire API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['success'] && jsonData['data'] != null) {
          print('✅ Questionnaire data parsed successfully');
          return QuestionnaireData.fromJson(jsonData['data']);
        } else {
          print('❌ API returned success=false or no data');
          return null;
        }
      } else {
        print('❌ API returned status code: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('💥 Error fetching questionnaire: $e');
      throw Exception('Error fetching questionnaire: $e');
    }
  }

  /// Submit questionnaire answers with auto-scoring
  static Future<QuestionnaireSubmissionResult?> submitQuestionnaireAnswers({
    required String questionnaireId,
    required Map<int, dynamic> userAnswers, // questionIndex -> answer
    required Map<int, int> timeSpent, // questionIndex -> timeSpent in seconds
  }) async {
    try {
      print('📤 Submitting questionnaire answers for ID: $questionnaireId');
      final url = Uri.parse('$baseUrl/api/questionnaires/$questionnaireId/submit');

      // Convert answers to the expected format
      final answersArray = userAnswers.entries.map((entry) {
        final questionIndex = entry.key;
        final answer = entry.value;
        final timeSpentForQuestion = timeSpent[questionIndex] ?? 0;

        return {
          'questionIndex': questionIndex,
          if (answer is String) 'textAnswer': answer,
          if (answer is int) 'selectedOption': answer,
          if (answer is bool) 'boolAnswer': answer,
          'timeSpent': timeSpentForQuestion,
        };
      }).toList();

      final body = {
        'answers': answersArray,
      };

      print('📤 Submit body: ${json.encode(body)}');

      final headers = await _authHeaders;
      final response = await http.post(
        url,
        headers: headers,
        body: json.encode(body),
      );

      print('📡 Submit Response Status: ${response.statusCode}');
      print('📡 Submit Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['success'] && jsonData['data'] != null) {
          return QuestionnaireSubmissionResult.fromJson(jsonData['data']);
        }
      }
      return null;
    } catch (e) {
      print('💥 Error submitting questionnaire answers: $e');
      return null;
    }
  }

  /// Get questionnaire results
  static Future<QuestionnaireSubmissionResult?> getQuestionnaireResults(String questionnaireId) async {
    try {
      print('📊 Fetching questionnaire results for ID: $questionnaireId');
      final url = Uri.parse('$baseUrl/api/questionnaires/$questionnaireId/results');

      final headers = await _authHeaders;
      final response = await http.get(url, headers: headers);

      print('📡 Results Response Status: ${response.statusCode}');
      print('📡 Results Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['success'] && jsonData['data'] != null) {
          return QuestionnaireSubmissionResult.fromJson(jsonData['data']);
        }
      }
      return null;
    } catch (e) {
      print('💥 Error fetching questionnaire results: $e');
      return null;
    }
  }

  /// Record questionnaire view for analytics
  static Future<void> recordQuestionnaireView(String questionnaireId) async {
    try {
      print('👁️ Recording questionnaire view for ID: $questionnaireId');
      final url = Uri.parse('$baseUrl/api/questionnaires/$questionnaireId/view');
      final response = await http.post(url, headers: _headers);

      if (response.statusCode == 200) {
        print('✅ Questionnaire view recorded successfully');
      } else {
        print('⚠️ Failed to record questionnaire view: ${response.statusCode}');
      }
    } catch (e) {
      print('💥 Error recording questionnaire view: $e');
      // Don't throw error for analytics - it's not critical
    }
  }

  /// Calculate questionnaire score and results
  static QuestionnaireResult calculateResults({
    required String questionnaireId,
    required QuestionnaireData questionnaireData,
    required List<QuestionnaireAnswer> answers,
    required int timeTaken,
  }) {
    int correctAnswers = 0;
    int totalPoints = 0;

    print('🧮 Calculating questionnaire results...');
    print('📊 Total questions: ${questionnaireData.questions.length}');
    print('📊 User answers: ${answers.length}');

    // Calculate correct answers and total points
    for (final question in questionnaireData.questions) {
      totalPoints += question.points;
      final userAnswer = answers.firstWhere(
        (answer) => answer.questionId == question.id,
        orElse: () => QuestionnaireAnswer(
          questionId: question.id,
          answer: -1,
          isCorrect: false,
          answeredAt: DateTime.now(),
        ),
      );
      
      if (userAnswer.isCorrect) {
        correctAnswers += question.points;
      }
    }

    final score = totalPoints > 0 ? ((correctAnswers / totalPoints) * 100).round() : 0;
    final passed = score >= questionnaireData.passingScore;

    print('📊 Calculation results:');
    print('   - Correct answers: $correctAnswers');
    print('   - Total points: $totalPoints');
    print('   - Score: $score%');
    print('   - Passing score: ${questionnaireData.passingScore}%');
    print('   - Passed: $passed');

    return QuestionnaireResult(
      questionnaireId: questionnaireId,
      score: score,
      totalQuestions: questionnaireData.questions.length,
      correctAnswers: correctAnswers,
      timeTaken: timeTaken,
      passed: passed,
      answers: answers,
      completedAt: DateTime.now(),
    );
  }

  /// Validate user's answer for a question
  static bool validateAnswer(QuestionnaireQuestion question, dynamic userAnswer) {
    if (userAnswer == null) return false;

    switch (question.questionType) {
      case QuestionType.multipleChoice:
        if (userAnswer is! int) return false;
        if (userAnswer < 0 || userAnswer >= question.options.length) return false;
        return userAnswer == question.correctAnswer;

      case QuestionType.textInput:
      case QuestionType.shortAnswer:
        if (userAnswer is! String) return false;
        if (question.correctAnswer is String) {
          return userAnswer.toLowerCase().trim() ==
                 (question.correctAnswer as String).toLowerCase().trim();
        }
        return false;

      case QuestionType.trueFalse:
        if (userAnswer is! bool) return false;
        return userAnswer == question.correctAnswer;
    }
  }

  /// Get formatted time string from seconds
  static String formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  /// Get difficulty color
  static String getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return '#4CAF50';
      case 'medium':
        return '#FF9800';
      case 'hard':
        return '#F44336';
      default:
        return '#6C5CE7';
    }
  }

  /// Get difficulty icon
  static String getDifficultyIcon(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return '🟢';
      case 'medium':
        return '🟡';
      case 'hard':
        return '🔴';
      default:
        return '⚪';
    }
  }
}
