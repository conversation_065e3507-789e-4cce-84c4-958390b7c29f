

import 'package:bolo_app/models/category.dart';
import 'package:bolo_app/models/topic.dart';
import 'package:bolo_app/models/video.dart';

class CategoryStats {
  final int totalVideos;
  final int totalDuration; // in seconds
  final int premiumVideos;
  final int freeVideos;
  final int totalTopics;

  CategoryStats({
    required this.totalVideos,
    required this.totalDuration,
    required this.premiumVideos,
    required this.freeVideos,
    required this.totalTopics,
  });

  factory CategoryStats.fromJson(Map<String, dynamic> json) {
    return CategoryStats(
      totalVideos: json['totalVideos'] ?? 0,
      totalDuration: json['totalDuration'] ?? 0,
      premiumVideos: json['premiumVideos'] ?? 0,
      freeVideos: json['freeVideos'] ?? 0,
      totalTopics: json['totalTopics'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalVideos': totalVideos,
      'totalDuration': totalDuration,
      'premiumVideos': premiumVideos,
      'freeVideos': freeVideos,
      'totalTopics': totalTopics,
    };
  }

  String get formattedDuration {
    final hours = totalDuration ~/ 3600;
    final minutes = (totalDuration % 3600) ~/ 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
}

class CategoryComplete {
  final Category category;
  final List<Topic> topics;
  final List<Video> videos;
  final CategoryStats stats;

  CategoryComplete({
    required this.category,
    required this.topics,
    required this.videos,
    required this.stats,
  });

  factory CategoryComplete.fromJson(Map<String, dynamic> json) {
    return CategoryComplete(
      category: Category.fromJson(json['category']),
      topics: (json['topics'] as List)
          .map((topic) => Topic.fromJson(topic))
          .toList(),
      videos: (json['videos'] as List)
          .map((video) => Video.fromJson(video))
          .toList(),
      stats: CategoryStats.fromJson(json['stats']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'category': category.toJson(),
      'topics': topics.map((topic) => topic.toJson()).toList(),
      'videos': videos.map((video) => video.toJson()).toList(),
      'stats': stats.toJson(),
    };
  }
}

class CategoryCompleteResponse {
  final bool success;
  final String message;
  final CategoryComplete? data;

  CategoryCompleteResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory CategoryCompleteResponse.fromJson(Map<String, dynamic> json) {
    return CategoryCompleteResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? CategoryComplete.fromJson(json['data']) : null,
    );
  }
}
