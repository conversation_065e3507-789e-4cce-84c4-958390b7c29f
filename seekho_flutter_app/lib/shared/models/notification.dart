class AppNotification {
  final String id;
  final String title;
  final String message;
  final String type;
  final bool isRead;
  final String priority;
  final DateTime createdAt;
  final Map<String, dynamic>? data;

  AppNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.isRead,
    required this.priority,
    required this.createdAt,
    this.data,
  });

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['_id'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      type: json['type'] ?? 'general',
      isRead: json['isRead'] ?? false,
      priority: json['priority'] ?? 'medium',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      data: json['data'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
      'message': message,
      'type': type,
      'isRead': isRead,
      'priority': priority,
      'createdAt': createdAt.toIso8601String(),
      'data': data,
    };
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  NotificationType get notificationType {
    switch (type.toLowerCase()) {
      case 'new_content':
        return NotificationType.newContent;
      case 'subscription':
        return NotificationType.subscription;
      case 'achievement':
        return NotificationType.achievement;
      case 'reminder':
        return NotificationType.reminder;
      default:
        return NotificationType.general;
    }
  }

  NotificationPriority get notificationPriority {
    switch (priority.toLowerCase()) {
      case 'high':
        return NotificationPriority.high;
      case 'low':
        return NotificationPriority.low;
      default:
        return NotificationPriority.medium;
    }
  }
}

enum NotificationType {
  general,
  newContent,
  subscription,
  achievement,
  reminder,
}

enum NotificationPriority {
  low,
  medium,
  high,
}

class NotificationsResponse {
  final bool success;
  final String message;
  final List<AppNotification>? data;
  final Map<String, dynamic>? pagination;

  NotificationsResponse({
    required this.success,
    required this.message,
    this.data,
    this.pagination,
  });

  factory NotificationsResponse.fromJson(Map<String, dynamic> json) {
    return NotificationsResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null
          ? (json['data'] as List)
              .map((item) => AppNotification.fromJson(item))
              .toList()
          : null,
      pagination: json['pagination'],
    );
  }
}

class UnreadCountResponse {
  final bool success;
  final String message;
  final int count;

  UnreadCountResponse({
    required this.success,
    required this.message,
    required this.count,
  });

  factory UnreadCountResponse.fromJson(Map<String, dynamic> json) {
    return UnreadCountResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      count: json['data']?['count'] ?? 0,
    );
  }
}
