import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';

class CountdownTimerService {
  static const String _timerKey = 'countdown_timer_end_time';
  static const int _timerDurationMinutes = 59;
  
  static Timer? _timer;
  static StreamController<String>? _timerController;
  static DateTime? _endTime;
  
  /// Get the timer stream to listen for updates
  static Stream<String> get timerStream {
    _timerController ??= StreamController<String>.broadcast();
    return _timerController!.stream;
  }
  
  /// Initialize the timer service
  static Future<void> initialize() async {
    await _loadTimerState();
    _startTimer();
  }
  
  /// Load timer state from shared preferences
  static Future<void> _loadTimerState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final endTimeString = prefs.getString(_timerKey);
      
      if (endTimeString != null) {
        _endTime = DateTime.parse(endTimeString);
        
        // Check if timer has expired
        if (_endTime!.isBefore(DateTime.now())) {
          // Timer expired, reset to new 59 minutes
          await _resetTimer();
        }
      } else {
        // No existing timer, create new one
        await _resetTimer();
      }
    } catch (e) {
      print('Error loading timer state: $e');
      await _resetTimer();
    }
  }
  
  /// Reset timer to 59 minutes from now
  static Future<void> _resetTimer() async {
    _endTime = DateTime.now().add(Duration(minutes: _timerDurationMinutes));
    await _saveTimerState();
  }
  
  /// Save timer state to shared preferences
  static Future<void> _saveTimerState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_endTime != null) {
        await prefs.setString(_timerKey, _endTime!.toIso8601String());
      }
    } catch (e) {
      print('Error saving timer state: $e');
    }
  }
  
  /// Start the countdown timer
  static void _startTimer() {
    _timer?.cancel();
    
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_endTime != null) {
        final now = DateTime.now();
        final difference = _endTime!.difference(now);
        
        if (difference.isNegative) {
          // Timer expired, reset to new 59 minutes
          _resetTimer().then((_) {
            final newDifference = _endTime!.difference(DateTime.now());
            final formattedTime = _formatDuration(newDifference);
            _timerController?.add(formattedTime);
          });
        } else {
          final formattedTime = _formatDuration(difference);
          _timerController?.add(formattedTime);
        }
      }
    });
    
    // Emit initial value
    if (_endTime != null) {
      final difference = _endTime!.difference(DateTime.now());
      if (!difference.isNegative) {
        final formattedTime = _formatDuration(difference);
        _timerController?.add(formattedTime);
      }
    }
  }
  
  /// Format duration to MM:SS format
  static String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
  
  /// Get current timer value synchronously
  static String getCurrentTime() {
    if (_endTime != null) {
      final difference = _endTime!.difference(DateTime.now());
      if (difference.isNegative) {
        return '59:00'; // Default when expired
      }
      return _formatDuration(difference);
    }
    return '59:00';
  }
  
  /// Manually reset timer (for testing or special cases)
  static Future<void> resetTimer() async {
    await _resetTimer();
    _startTimer();
  }
  
  /// Dispose resources
  static void dispose() {
    _timer?.cancel();
    _timerController?.close();
    _timer = null;
    _timerController = null;
  }
}
