import 'dart:convert';
import 'package:http/http.dart' as http;

/// Global HTTP client service that automatically adds required headers to all API requests
class HttpClientService {
  static const String _packageIdHeader = 'X-Package-ID';
  static const String _packageIdValue = 'com.gumbo.english';
  
  /// Get default headers that should be included in all requests
  static Map<String, String> _getDefaultHeaders() {
    return {
      'Content-Type': 'application/json',
      _packageIdHeader: _packageIdValue,
    };
  }
  
  /// Merge custom headers with default headers
  static Map<String, String> _mergeHeaders(Map<String, String>? customHeaders) {
    final defaultHeaders = _getDefaultHeaders();
    if (customHeaders != null) {
      defaultHeaders.addAll(customHeaders);
    }
    return defaultHeaders;
  }
  
  /// Make a GET request with automatic header injection
  static Future<http.Response> get(
    Uri url, {
    Map<String, String>? headers,
  }) async {
    final mergedHeaders = _mergeHeaders(headers);
    return await http.get(url, headers: mergedHeaders);
  }
  
  /// Make a POST request with automatic header injection
  static Future<http.Response> post(
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
  }) async {
    final mergedHeaders = _mergeHeaders(headers);

    // Debug logging for subscription calls
    if (url.path.contains('subscription')) {
      print('🔍 DEBUG: POST Headers being sent:');
      mergedHeaders.forEach((key, value) {
        print('🔍   $key: $value');
      });
    }

    return await http.post(
      url,
      headers: mergedHeaders,
      body: body,
      encoding: encoding,
    );
  }
  
  /// Make a PUT request with automatic header injection
  static Future<http.Response> put(
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
  }) async {
    final mergedHeaders = _mergeHeaders(headers);
    return await http.put(
      url,
      headers: mergedHeaders,
      body: body,
      encoding: encoding,
    );
  }
  
  /// Make a PATCH request with automatic header injection
  static Future<http.Response> patch(
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
  }) async {
    final mergedHeaders = _mergeHeaders(headers);
    return await http.patch(
      url,
      headers: mergedHeaders,
      body: body,
      encoding: encoding,
    );
  }
  
  /// Make a DELETE request with automatic header injection
  static Future<http.Response> delete(
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
  }) async {
    final mergedHeaders = _mergeHeaders(headers);
    return await http.delete(
      url,
      headers: mergedHeaders,
      body: body,
      encoding: encoding,
    );
  }
  
  /// Make a HEAD request with automatic header injection
  static Future<http.Response> head(
    Uri url, {
    Map<String, String>? headers,
  }) async {
    final mergedHeaders = _mergeHeaders(headers);
    return await http.head(url, headers: mergedHeaders);
  }
}
