import 'package:flutter/material.dart';
import '../services/new_subscription_service.dart';
import '../screens/subscription_screen.dart';
import '../screens/unified_subscription_screen.dart';

/// Centralized utility class for handling premium content access checks
/// and subscription navigation consistently across the app
class PremiumContentHelper {
  
  /// Check if user has access to premium content
  /// This includes test user checks and subscription status
  static Future<bool> hasAccess() async {
    return await NewSubscriptionService.hasAccessToPremiumContent();
  }

  /// Handle premium content access with automatic navigation to subscription screen
  /// Returns true if user has access or content is not premium
  /// Returns false if user needs subscription (and navigates to subscription screen)
  static Future<bool> handlePremiumAccess({
    required BuildContext context,
    required bool isPremium,
    bool useUnifiedScreen = true,
    bool popCurrentScreen = false,
    String? debugContentName,
  }) async {
    // If content is not premium, allow access
    if (!isPremium) {
      return true;
    }

    if (debugContentName != null) {
      print('🔒 $debugContentName is premium, checking access...');
    }

    // Check if user has access to premium content
    final hasAccess = await NewSubscriptionService.hasAccessToPremiumContent();

    if (!hasAccess) {
      if (debugContentName != null) {
        print('❌ Premium $debugContentName requires subscription. Redirecting to subscription screen.');
      }

      if (context.mounted) {
        // Pop current screen if requested (useful for viewers that should close)
        if (popCurrentScreen) {
          Navigator.pop(context);
        }

        // Navigate to appropriate subscription screen
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => useUnifiedScreen 
                ? const UnifiedSubscriptionScreen()
                : const SubscriptionScreen(),
          ),
        );
      }
      return false;
    } else {
      if (debugContentName != null) {
        print('✅ User has premium access, proceeding with $debugContentName');
      }
      return true;
    }
  }

  /// Handle premium video access
  static Future<bool> handleVideoAccess({
    required BuildContext context,
    required bool isPremium,
    String? videoTitle,
    bool useUnifiedScreen = true,
    bool popCurrentScreen = false,
  }) async {
    return await handlePremiumAccess(
      context: context,
      isPremium: isPremium,
      useUnifiedScreen: useUnifiedScreen,
      popCurrentScreen: popCurrentScreen,
      debugContentName: videoTitle != null ? 'video "$videoTitle"' : 'video',
    );
  }

  /// Handle premium text content access
  static Future<bool> handleTextContentAccess({
    required BuildContext context,
    required bool isPremium,
    String? contentTitle,
    bool useUnifiedScreen = true,
    bool popCurrentScreen = false,
  }) async {
    return await handlePremiumAccess(
      context: context,
      isPremium: isPremium,
      useUnifiedScreen: useUnifiedScreen,
      popCurrentScreen: popCurrentScreen,
      debugContentName: contentTitle != null ? 'text content "$contentTitle"' : 'text content',
    );
  }

  /// Handle premium MCQ access
  static Future<bool> handleMCQAccess({
    required BuildContext context,
    required bool isPremium,
    String? mcqTitle,
    bool useUnifiedScreen = true,
    bool popCurrentScreen = false,
  }) async {
    return await handlePremiumAccess(
      context: context,
      isPremium: isPremium,
      useUnifiedScreen: useUnifiedScreen,
      popCurrentScreen: popCurrentScreen,
      debugContentName: mcqTitle != null ? 'MCQ "$mcqTitle"' : 'MCQ',
    );
  }

  /// Handle premium questionnaire access
  static Future<bool> handleQuestionnaireAccess({
    required BuildContext context,
    required bool isPremium,
    String? questionnaireTitle,
    bool useUnifiedScreen = false, // Questionnaires typically use regular subscription screen
    bool popCurrentScreen = true, // Questionnaires typically pop the viewer
  }) async {
    return await handlePremiumAccess(
      context: context,
      isPremium: isPremium,
      useUnifiedScreen: useUnifiedScreen,
      popCurrentScreen: popCurrentScreen,
      debugContentName: questionnaireTitle != null ? 'questionnaire "$questionnaireTitle"' : 'questionnaire',
    );
  }

  /// Handle premium module access
  static Future<bool> handleModuleAccess({
    required BuildContext context,
    required bool isPremium,
    String? moduleTitle,
    bool useUnifiedScreen = true,
    bool popCurrentScreen = false,
  }) async {
    return await handlePremiumAccess(
      context: context,
      isPremium: isPremium,
      useUnifiedScreen: useUnifiedScreen,
      popCurrentScreen: popCurrentScreen,
      debugContentName: moduleTitle != null ? 'module "$moduleTitle"' : 'module',
    );
  }

  /// Show premium indicator widget
  static Widget buildPremiumIndicator({
    double iconSize = 16,
    double fontSize = 12,
    Color? color,
    bool showText = true,
  }) {
    final indicatorColor = color ?? Colors.orange.withValues(alpha: 0.8);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.lock,
          size: iconSize,
          color: indicatorColor,
        ),
        if (showText) ...[
          const SizedBox(width: 4),
          Text(
            'Premium',
            style: TextStyle(
              color: indicatorColor,
              fontSize: fontSize,
            ),
          ),
        ],
      ],
    );
  }

  /// Check if content should show premium indicator
  static bool shouldShowPremiumIndicator(bool isPremium) {
    return isPremium;
  }

  /// Get premium lock overlay widget for content cards
  static Widget buildPremiumLockOverlay({
    VoidCallback? onTap,
    double opacity = 0.8,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: opacity),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Center(
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.lock,
                  color: Colors.white,
                  size: 32,
                ),
                SizedBox(height: 8),
                Text(
                  'Premium',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'Tap to Subscribe',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Handle premium content tap with subscription navigation
  static Future<void> handlePremiumContentTap({
    required BuildContext context,
    bool useUnifiedScreen = true,
  }) async {
    if (context.mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => useUnifiedScreen 
              ? const UnifiedSubscriptionScreen()
              : const SubscriptionScreen(),
        ),
      );
    }
  }
}
