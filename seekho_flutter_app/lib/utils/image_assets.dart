/// Image asset paths for the English Guru  app
class ImageAssets {
  // Base path
  static const String _basePath = 'assets/images';
  
  // App branding
  static const String appLogo = '$_basePath/app_logo.png';
  static const String appIcon = '$_basePath/app_icon.png';
  static const String splashLogo = '$_basePath/splash_logo.png';
  
  // Authentication
  static const String googleLogo = '$_basePath/google_logo.png';
  static const String loginBackground = '$_basePath/login_background.png';
  
  // UI Elements
  static const String placeholder = '$_basePath/placeholder.png';
  static const String noImage = '$_basePath/no_image.png';
  static const String premiumBadge = '$_basePath/premium_badge.png';
  
  // Category icons
  static const String categoryDefault = '$_basePath/icons/category_default.png';
  static const String categoryTech = '$_basePath/icons/category_tech.png';
  static const String categoryLanguage = '$_basePath/icons/category_language.png';
  static const String categoryGaming = '$_basePath/icons/category_gaming.png';
  static const String categoryAstrology = '$_basePath/icons/category_astrology.png';
  
  // Navigation icons
  static const String homeIcon = '$_basePath/icons/home.png';
  static const String libraryIcon = '$_basePath/icons/library.png';
  static const String profileIcon = '$_basePath/icons/profile.png';
  static const String searchIcon = '$_basePath/icons/search.png';
  
  // Subscription
  static const String premiumCrown = '$_basePath/premium_crown.png';
  static const String subscriptionBanner = '$_basePath/subscription_banner.png';
  
  // Helper method to check if asset exists
  static bool assetExists(String path) {
    // In a real app, you might want to implement actual asset checking
    return true;
  }
  
  // Helper method to get category icon based on category name
  static String getCategoryIcon(String categoryName) {
    switch (categoryName.toLowerCase()) {
      case 'technology':
      case 'tech':
        return categoryTech;
      case 'language':
      case 'english':
        return categoryLanguage;
      case 'gaming':
      case 'games':
        return categoryGaming;
      case 'astrology':
        return categoryAstrology;
      default:
        return categoryDefault;
    }
  }
}
