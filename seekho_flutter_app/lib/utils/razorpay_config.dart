class RazorpayConfig {
  // TODO: Replace with your actual Razorpay keys
  // For testing, use test keys (starts with rzp_test_)
  // For production, use live keys (starts with rzp_live_)

  static const String testKeyId = 'rzp_test_1DP5mmOlF5G5ag'; // Keep for future testing
  static const String liveKeyId = 'rzp_live_EWIcFTdUd0CymA';

  // Set this to true for debugging UPI issues - TEST MODE FOR DEBUGGING
  static const bool isTestMode = false;

  static String get keyId => isTestMode ? testKeyId : liveKeyId;

  // Razorpay webhook secret (for backend verification)
  static const String webhookSecret = 'whsec_seekho_2024_secure_webhook_secret_key_12345';
  // Company details for Razorpay checkout
  static const String companyName = 'English Guru  App';
  static const String companyLogo = ''; // URL to your company logo
  static const String themeColor = '#F7B801';

  // Test card details for testing
  static const Map<String, String> testCards = {
    'visa_success': '4111 1111 1111 1111',
    'mastercard_success': '5555 5555 5555 4444',
    'visa_failure': '4000 0000 0000 0002',
  };

  // Test UPI IDs
  static const Map<String, String> testUpiIds = {
    'success': 'success@razorpay',
    'failure': 'failure@razorpay',
  };
}

// Instructions for setting up Razorpay:
/*
1. Go to https://dashboard.razorpay.com/
2. Create an account or login
3. Go to Settings > API Keys
4. Generate API Keys
5. Copy the Key ID and replace 'YOUR_TEST_KEY_HERE' above
6. For production, generate live keys and replace 'YOUR_LIVE_KEY_HERE'
7. Set isTestMode to false for production

Note: Never commit your live keys to version control!
Consider using environment variables or secure storage for production keys.
*/
