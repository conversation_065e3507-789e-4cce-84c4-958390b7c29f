import '../../../models/video.dart';

class WatchHistoryItem {
  final Video video;
  final DateTime watchedAt;
  final int progress; // in seconds
  final bool completed;
  final int duration; // total video duration in seconds

  WatchHistoryItem({
    required this.video,
    required this.watchedAt,
    required this.progress,
    required this.completed,
    required this.duration,
  });

  factory WatchHistoryItem.fromJson(Map<String, dynamic> json) {
    return WatchHistoryItem(
      video: Video.fromJson(json['video']),
      watchedAt: DateTime.parse(json['watchedAt']),
      progress: json['progress'] ?? 0,
      completed: json['completed'] ?? false,
      duration: json['duration'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'video': video.toJson(),
      'watchedAt': watchedAt.toIso8601String(),
      'progress': progress,
      'completed': completed,
      'duration': duration,
    };
  }

  double get progressPercentage {
    if (duration == 0) return 0.0;
    return (progress / duration).clamp(0.0, 1.0);
  }

  String get formattedProgress {
    final progressMinutes = progress ~/ 60;
    final progressSeconds = progress % 60;
    final totalMinutes = duration ~/ 60;
    final totalSeconds = duration % 60;
    
    return '${progressMinutes.toString().padLeft(2, '0')}:${progressSeconds.toString().padLeft(2, '0')} / ${totalMinutes.toString().padLeft(2, '0')}:${totalSeconds.toString().padLeft(2, '0')}';
  }
}

class WatchHistoryResponse {
  final bool success;
  final String message;
  final List<WatchHistoryItem>? data;
  final Map<String, dynamic>? pagination;

  WatchHistoryResponse({
    required this.success,
    required this.message,
    this.data,
    this.pagination,
  });

  factory WatchHistoryResponse.fromJson(Map<String, dynamic> json) {
    return WatchHistoryResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null
          ? (json['data']['history'] as List)
              .map((item) => WatchHistoryItem.fromJson(item))
              .toList()
          : null,
      pagination: json['data']?['pagination'],
    );
  }
}
