class UserStats {
  final int videosWatched;
  final int totalWatchTime; // in seconds
  final int completedCourses;
  final int favoriteVideos;
  final DateTime joinedDate;
  final int currentStreak;
  final int totalBookmarks;
  final double averageProgress;

  UserStats({
    required this.videosWatched,
    required this.totalWatchTime,
    required this.completedCourses,
    required this.favoriteVideos,
    required this.joinedDate,
    required this.currentStreak,
    required this.totalBookmarks,
    required this.averageProgress,
  });

  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      videosWatched: json['videosWatched'] ?? 0,
      totalWatchTime: json['totalWatchTime'] ?? 0,
      completedCourses: json['completedCourses'] ?? 0,
      favoriteVideos: json['favoriteVideos'] ?? 0,
      joinedDate: DateTime.parse(json['joinedDate'] ?? DateTime.now().toIso8601String()),
      currentStreak: json['currentStreak'] ?? 0,
      totalBookmarks: json['totalBookmarks'] ?? 0,
      averageProgress: (json['averageProgress'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'videosWatched': videosWatched,
      'totalWatchTime': totalWatchTime,
      'completedCourses': completedCourses,
      'favoriteVideos': favoriteVideos,
      'joinedDate': joinedDate.toIso8601String(),
      'currentStreak': currentStreak,
      'totalBookmarks': totalBookmarks,
      'averageProgress': averageProgress,
    };
  }

  String get formattedWatchTime {
    final hours = totalWatchTime ~/ 3600;
    final minutes = (totalWatchTime % 3600) ~/ 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
}

class UserStatsResponse {
  final bool success;
  final String message;
  final UserStats? data;

  UserStatsResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory UserStatsResponse.fromJson(Map<String, dynamic> json) {
    return UserStatsResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? UserStats.fromJson(json['data']) : null,
    );
  }
}
