// Comprehensive subscription response models based on actual API responses

class SubscriptionPlansResponse {
  final List<SubscriptionPlanData> subscriptionList;
  final bool premiumUser;
  final int premiumTill;
  final String subscriptionStatus;

  SubscriptionPlansResponse({
    required this.subscriptionList,
    required this.premiumUser,
    required this.premiumTill,
    required this.subscriptionStatus,
  });

  factory SubscriptionPlansResponse.fromJson(Map<String, dynamic> json) {
    return SubscriptionPlansResponse(
      subscriptionList: (json['subscriptionList'] as List<dynamic>?)
          ?.map((item) => SubscriptionPlanData.fromJson(item))
          .toList() ?? [],
      premiumUser: json['premiumUser'] ?? false,
      premiumTill: json['premiumTill'] ?? 0,
      subscriptionStatus: json['subscriptionStatus'] ?? '',
    );
  }
}

class SubscriptionPlanData {
  final String packageId;
  final String label;
  final int price;
  final int priceAfterTax;
  final int strikePrice;
  final bool freeTrial;
  final int trialPrice;
  final String planId;
  final int validityInDays;

  SubscriptionPlanData({
    required this.packageId,
    required this.label,
    required this.price,
    required this.priceAfterTax,
    required this.strikePrice,
    required this.freeTrial,
    required this.trialPrice,
    required this.planId,
    required this.validityInDays,
  });

  factory SubscriptionPlanData.fromJson(Map<String, dynamic> json) {
    return SubscriptionPlanData(
      packageId: json['packageId'] ?? '',
      label: json['label'] ?? '',
      price: json['price'] ?? 0,
      priceAfterTax: json['priceAfterTax'] ?? 0,
      strikePrice: json['strikePrice'] ?? 0,
      freeTrial: json['freeTrial'] ?? false,
      trialPrice: json['trialPrice'] ?? 0,
      planId: json['planId'] ?? '',
      validityInDays: json['validityInDays'] ?? 0,
    );
  }

  // Convert to display format
  String get displayName => label;
  String get displayPrice => freeTrial ? '₹$trialPrice' : '₹$priceAfterTax';
  String get originalPrice => '₹$priceAfterTax';
  String get planType => label.toLowerCase().contains('month') ? 'monthly' : 'yearly';
  String get duration => label.toLowerCase().contains('month') ? '1 month' : '1 year';

  List<String> get features => [
    'Access to all premium videos',
    'Unlimited downloads',
    'Ad-free experience',
    'HD video quality',
    if (planType == 'yearly') 'Save 60% compared to monthly',
  ];
}

class TrialEligibilityResponse {
  final bool success;
  final TrialEligibilityData? data;
  final String? message;

  TrialEligibilityResponse({
    required this.success,
    this.data,
    this.message,
  });

  factory TrialEligibilityResponse.fromJson(Map<String, dynamic> json) {
    return TrialEligibilityResponse(
      success: json['success'] ?? false,
      data: json['data'] != null ? TrialEligibilityData.fromJson(json['data']) : null,
      message: json['message'],
    );
  }
}

class TrialEligibilityData {
  final bool isTrialEligible;
  final bool hasUsedTrial;
  final String? trialUsedAt;
  final dynamic alternativeOptions;

  TrialEligibilityData({
    required this.isTrialEligible,
    required this.hasUsedTrial,
    this.trialUsedAt,
    this.alternativeOptions,
  });

  factory TrialEligibilityData.fromJson(Map<String, dynamic> json) {
    return TrialEligibilityData(
      isTrialEligible: json['isTrialEligible'] ?? false,
      hasUsedTrial: json['hasUsedTrial'] ?? false,
      trialUsedAt: json['trialUsedAt'],
      alternativeOptions: json['alternativeOptions'],
    );
  }
}

class TrialWithMandateResponse {
  final bool success;
  final String? message;
  final TrialWithMandateData? data;

  TrialWithMandateResponse({
    required this.success,
    this.message,
    this.data,
  });

  factory TrialWithMandateResponse.fromJson(Map<String, dynamic> json) {
    return TrialWithMandateResponse(
      success: json['success'] ?? false,
      message: json['message'],
      data: json['data'] != null ? TrialWithMandateData.fromJson(json['data']) : null,
    );
  }
}

class TrialWithMandateData {
  final String subscriptionId;
  final String razorpaySubscriptionId;
  final int trialAmount;
  final int mainAmount;
  final int trialPeriod;
  final bool autoConversion;
  final String mainBillingStartsAt;
  final String description;
  final String razorpayKeyId;
  final TrialSubscriptionDetails? subscriptionDetails;

  TrialWithMandateData({
    required this.subscriptionId,
    required this.razorpaySubscriptionId,
    required this.trialAmount,
    required this.mainAmount,
    required this.trialPeriod,
    required this.autoConversion,
    required this.mainBillingStartsAt,
    required this.description,
    required this.razorpayKeyId,
    this.subscriptionDetails,
  });

  factory TrialWithMandateData.fromJson(Map<String, dynamic> json) {
    return TrialWithMandateData(
      subscriptionId: json['subscriptionId'] ?? '',
      razorpaySubscriptionId: json['razorpaySubscriptionId'] ?? '',
      trialAmount: json['trialAmount'] ?? 0,
      mainAmount: json['mainAmount'] ?? 0,
      trialPeriod: json['trialPeriod'] ?? 0,
      autoConversion: json['autoConversion'] ?? false,
      mainBillingStartsAt: json['mainBillingStartsAt'] ?? '',
      description: json['description'] ?? '',
      razorpayKeyId: json['razorpayKeyId'] ?? '',
      subscriptionDetails: json['subscriptionDetails'] != null
          ? TrialSubscriptionDetails.fromJson(json['subscriptionDetails'])
          : null,
    );
  }
}

class TrialSubscriptionDetails {
  final String customerId;
  final String planId;
  final int totalCount;
  final Map<String, dynamic> notes;

  TrialSubscriptionDetails({
    required this.customerId,
    required this.planId,
    required this.totalCount,
    required this.notes,
  });

  factory TrialSubscriptionDetails.fromJson(Map<String, dynamic> json) {
    return TrialSubscriptionDetails(
      customerId: json['customerId'] ?? '',
      planId: json['planId'] ?? '',
      totalCount: json['totalCount'] ?? 0,
      notes: Map<String, dynamic>.from(json['notes'] ?? {}),
    );
  }
}

class RegularSubscriptionResponse {
  final bool success;
  final RegularSubscriptionData? data;
  final String? message;

  RegularSubscriptionResponse({
    required this.success,
    this.data,
    this.message,
  });

  factory RegularSubscriptionResponse.fromJson(Map<String, dynamic> json) {
    return RegularSubscriptionResponse(
      success: json['success'] ?? false,
      data: json['data'] != null ? RegularSubscriptionData.fromJson(json['data']) : null,
      message: json['message'],
    );
  }
}

class RegularSubscriptionData {
  final String? subscriptionId; // For recurring payments
  final String? orderId; // For one-time payments
  final String? razorpaySubscriptionId; // Razorpay subscription ID for recurring payments
  final String? razorpayOrderId; // Razorpay order ID for one-time payments
  final int amount;
  final String currency;
  final String plan;
  final String razorpayKeyId;
  final String type;
  final RegularSubscriptionDetails? subscriptionDetails;

  RegularSubscriptionData({
    this.subscriptionId,
    this.orderId,
    this.razorpaySubscriptionId,
    this.razorpayOrderId,
    required this.amount,
    required this.currency,
    required this.plan,
    required this.razorpayKeyId,
    required this.type,
    this.subscriptionDetails,
  });

  factory RegularSubscriptionData.fromJson(Map<String, dynamic> json) {
    return RegularSubscriptionData(
      subscriptionId: json['subscriptionId'],
      orderId: json['orderId'],
      razorpaySubscriptionId: json['razorpaySubscriptionId'],
      razorpayOrderId: json['razorpayOrderId'],
      amount: json['amount'] ?? 0,
      currency: json['currency'] ?? 'INR',
      plan: json['plan'] ?? '',
      razorpayKeyId: json['razorpayKeyId'] ?? '',
      type: json['type'] ?? '',
      subscriptionDetails: json['subscriptionDetails'] != null
          ? RegularSubscriptionDetails.fromJson(json['subscriptionDetails'])
          : null,
    );
  }

  // Helper methods to determine payment type
  bool get isRecurring => razorpaySubscriptionId != null && razorpaySubscriptionId!.isNotEmpty;
  bool get isOneTime => razorpayOrderId != null && razorpayOrderId!.isNotEmpty;
  String get paymentId => isRecurring ? razorpaySubscriptionId! : razorpayOrderId!;
}

class RegularSubscriptionDetails {
  final String dbSubscriptionId;
  final String razorpaySubscriptionId;
  final String customerId;

  RegularSubscriptionDetails({
    required this.dbSubscriptionId,
    required this.razorpaySubscriptionId,
    required this.customerId,
  });

  factory RegularSubscriptionDetails.fromJson(Map<String, dynamic> json) {
    return RegularSubscriptionDetails(
      dbSubscriptionId: json['dbSubscriptionId'] ?? '',
      razorpaySubscriptionId: json['razorpaySubscriptionId'] ?? '',
      customerId: json['customerId'] ?? '',
    );
  }
}

class SubscriptionStatusResponse {
  final bool success;
  final SubscriptionStatusData? data;
  final String? message;

  SubscriptionStatusResponse({
    required this.success,
    this.data,
    this.message,
  });

  factory SubscriptionStatusResponse.fromJson(Map<String, dynamic> json) {
    return SubscriptionStatusResponse(
      success: json['success'] ?? false,
      data: json['data'] != null ? SubscriptionStatusData.fromJson(json['data']) : null,
      message: json['message'],
    );
  }
}

class SubscriptionStatusData {
  final bool hasSubscription;
  final bool isActive;
  final dynamic subscription; // Can be null

  SubscriptionStatusData({
    required this.hasSubscription,
    required this.isActive,
    this.subscription,
  });

  factory SubscriptionStatusData.fromJson(Map<String, dynamic> json) {
    return SubscriptionStatusData(
      hasSubscription: json['hasSubscription'] ?? false,
      isActive: json['isActive'] ?? false,
      subscription: json['subscription'],
    );
  }
}
