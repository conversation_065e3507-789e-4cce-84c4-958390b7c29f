class PaymentData {
  final String razorpayPaymentId;
  final String razorpayOrderId;
  final String razorpaySignature;
  final String plan;

  PaymentData({
    required this.razorpayPaymentId,
    required this.razorpayOrderId,
    required this.razorpaySignature,
    required this.plan,
  });

  Map<String, dynamic> toJson() {
    return {
      'razorpay_payment_id': razorpayPaymentId,
      'razorpay_order_id': razorpayOrderId,
      'razorpay_signature': razorpaySignature,
      'plan': plan,
    };
  }
}

class PaymentVerificationResponse {
  final bool success;
  final String message;
  final VerificationData? data;

  PaymentVerificationResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory PaymentVerificationResponse.fromJson(Map<String, dynamic> json) {
    return PaymentVerificationResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null 
          ? VerificationData.fromJson(json['data'])
          : null,
    );
  }
}

class VerificationData {
  final String id;
  final String plan;
  final String status;
  final DateTime startDate;
  final DateTime endDate;
  final bool autoRenew;

  VerificationData({
    required this.id,
    required this.plan,
    required this.status,
    required this.startDate,
    required this.endDate,
    required this.autoRenew,
  });

  factory VerificationData.fromJson(Map<String, dynamic> json) {
    return VerificationData(
      id: json['id'] ?? '',
      plan: json['plan'] ?? '',
      status: json['status'] ?? '',
      startDate: DateTime.parse(json['startDate'] ?? DateTime.now().toIso8601String()),
      endDate: DateTime.parse(json['endDate'] ?? DateTime.now().toIso8601String()),
      autoRenew: json['autoRenew'] ?? false,
    );
  }
}
