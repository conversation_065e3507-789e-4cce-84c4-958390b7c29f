import 'dart:convert';
import 'package:bolo_app/services/auth_service.dart';
import 'package:bolo_app/utils/constants.dart';
import 'package:http/http.dart' as http;

import '../models/subscription_response.dart';


class NewSubscriptionService {

  // Get subscription plans from API
  static Future<SubscriptionPlansResponse> getSubscriptionPlans() async {
    print('📡 Fetching subscription plans...');

    try {
      final url = '${ApiConstants.baseUrl}${ApiConstants.subscriptionPlans}';
      print('📡 Making API call to: $url');

      final response = await http.get(
        Uri.parse(url),
      );

      print('📡 Response status: ${response.statusCode}');
      print('📡 Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return SubscriptionPlansResponse.fromJson(responseData);
      } else {
        throw Exception('Failed to fetch subscription plans: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Error fetching subscription plans: $e');
      throw Exception('Error fetching subscription plans: $e');
    }
  }

  // Check trial eligibility
  static Future<TrialEligibilityResponse> checkTrialEligibility() async {
    print('📡 Checking trial eligibility...');

    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        return TrialEligibilityResponse(
          success: false,
          message: 'User not authenticated',
        );
      }

      final url = '${ApiConstants.baseUrl}${ApiConstants.trialEligibility}';
      print('📡 Making API call to: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      print('📡 Response status: ${response.statusCode}');
      print('📡 Response body: ${response.body}');

      final responseData = json.decode(response.body);
      return TrialEligibilityResponse.fromJson(responseData);
    } catch (e) {
      print('❌ Error checking trial eligibility: $e');
      return TrialEligibilityResponse(
        success: false,
        message: 'Error checking trial eligibility: $e',
      );
    }
  }

  // Get subscription status
  static Future<SubscriptionStatusResponse> getSubscriptionStatus() async {
    print('📡 Fetching subscription status...');

    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        return SubscriptionStatusResponse(
          success: false,
          message: 'User not authenticated',
        );
      }

      final url = '${ApiConstants.baseUrl}${ApiConstants.subscriptionStatus}';
      print('📡 Making API call to: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      print('📡 Response status: ${response.statusCode}');
      print('📡 Response body: ${response.body}');

      final responseData = json.decode(response.body);
      return SubscriptionStatusResponse.fromJson(responseData);
    } catch (e) {
      print('❌ Error fetching subscription status: $e');
      return SubscriptionStatusResponse(
        success: false,
        message: 'Error fetching subscription status: $e',
      );
    }
  }

  // Create trial subscription with mandate
  static Future<TrialWithMandateResponse> createTrialWithMandate({
    required String name,
    required String email,
    required String phone,
  }) async {
    print('📡 Creating trial subscription with mandate...');

    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        return TrialWithMandateResponse(
          success: false,
          message: 'User not authenticated',
        );
      }

  final url = '${ApiConstants.baseUrl}${ApiConstants.createTrialWithMandate}';
      // final url = '${ApiConstants.baseUrl}${ApiConstants.createTrialWithMandate}';
      final requestBody = {
        'name': name,
        'email': email,
        'phone': phone,
      };

      print('📡 Making API call to: $url');
      print('📋 Request body: $requestBody');

      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode(requestBody),
      );

      print('📡 Response status: ${response.statusCode}');
      print('📡 Response body: ${response.body}');

      final responseData = json.decode(response.body);
      return TrialWithMandateResponse.fromJson(responseData);
    } catch (e) {
      print('❌ Error creating trial with mandate: $e');
      return TrialWithMandateResponse(
        success: false,
        message: 'Error creating trial with mandate: $e',
      );
    }
  }

  // Create regular subscription order (with recurring option)
  static Future<RegularSubscriptionResponse> createRegularSubscription({
    required String planType, // 'monthly' or 'yearly'
    required bool recurring, // true for subscription, false for one-time payment
  }) async {
    print('📡 Creating regular subscription order (recurring: $recurring)...');

    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        return RegularSubscriptionResponse(
          success: false,
          message: 'User not authenticated',
        );
      }

      final url = '${ApiConstants.baseUrl}${ApiConstants.createOrder}';
      final requestBody = {
        'plan': planType,
        'recurring': recurring,
      };

      print('📡 Making API call to: $url');
      print('📋 Request body: $requestBody');

      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode(requestBody),
      );

      print('📡 Response status: ${response.statusCode}');
      print('📡 Response body: ${response.body}');

      final responseData = json.decode(response.body);
      final subscriptionResponse = RegularSubscriptionResponse.fromJson(responseData);

      // Debug: Log the amount received from backend
      if (subscriptionResponse.data != null) {
        print('🔍 DEBUG: Amount from backend: ${subscriptionResponse.data!.amount}');
        print('🔍 DEBUG: Plan: ${subscriptionResponse.data!.plan}');
        print('🔍 DEBUG: Type: ${subscriptionResponse.data!.type}');
        print('🔍 DEBUG: Recurring: $recurring');
        print('🔍 DEBUG: OrderId: ${subscriptionResponse.data!.orderId}');
        print('🔍 DEBUG: SubscriptionId: ${subscriptionResponse.data!.subscriptionId}');
      }

      return subscriptionResponse;
    } catch (e) {
      print('❌ Error creating regular subscription: $e');
      return RegularSubscriptionResponse(
        success: false,
        message: 'Error creating regular subscription: $e',
      );
    }
  }

  // Verify payment for one-time payments (order_id)
  static Future<Map<String, dynamic>> verifyOneTimePayment({
    required String paymentId,
    required String orderId,
    required String signature,
    required String plan, // 'monthly' or 'yearly'
  }) async {
    print('📡 Verifying one-time payment...');

    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'User not authenticated',
        };
      }

      final url = '${ApiConstants.baseUrl}${ApiConstants.verifyPayment}';
      final requestBody = {
        'razorpay_payment_id': paymentId,
        'razorpay_order_id': orderId,
        'razorpay_signature': signature,
        'plan': plan,
      };

      print('📡 Making API call to: $url');
      print('📋 Request body (one-time): $requestBody');

      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode(requestBody),
      );

      print('📡 Response status: ${response.statusCode}');
      print('📡 Response body: ${response.body}');

      return json.decode(response.body);
    } catch (e) {
      print('❌ Error verifying one-time payment: $e');
      return {
        'success': false,
        'message': 'Error verifying one-time payment: $e',
      };
    }
  }

  // Verify payment for recurring subscriptions (subscription_id)
  static Future<Map<String, dynamic>> verifyRecurringPayment({
    required String paymentId,
    required String subscriptionId,
    required String signature,
    required String plan, // 'monthly' or 'yearly'
  }) async {
    print('📡 Verifying recurring payment...');

    try {
      final token = await AuthService.getAccessToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'User not authenticated',
        };
      }

      final url = '${ApiConstants.baseUrl}${ApiConstants.verifyPayment}';
      final requestBody = {
        'razorpay_payment_id': paymentId,
        'razorpay_subscription_id': subscriptionId,
        'razorpay_signature': signature,
        'plan': plan,
      };

      print('📡 Making API call to: $url');
      print('📋 Request body (recurring): $requestBody');

      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode(requestBody),
      );

      print('📡 Response status: ${response.statusCode}');
      print('📡 Response body: ${response.body}');

      return json.decode(response.body);
    } catch (e) {
      print('❌ Error verifying recurring payment: $e');
      return {
        'success': false,
        'message': 'Error verifying recurring payment: $e',
      };
    }
  }

  // Legacy method for backward compatibility - automatically detects payment type
  static Future<Map<String, dynamic>> verifyPayment({
    required String paymentId,
    required String idValue, // Can be either order_id or subscription_id
    String? signature,
    String? plan,
    bool isRecurring = false, // Specify payment type
  }) async {
    print('📡 Verifying payment (legacy method)...');

    if (signature == null || plan == null) {
      return {
        'success': false,
        'message': 'Missing required parameters: signature and plan',
      };
    }

    if (isRecurring) {
      return verifyRecurringPayment(
        paymentId: paymentId,
        subscriptionId: idValue,
        signature: signature,
        plan: plan,
      );
    } else {
      return verifyOneTimePayment(
        paymentId: paymentId,
        orderId: idValue,
        signature: signature,
        plan: plan,
      );
    }
  }

  // List of premium test users who get free access
  static bool _isPremiumTestUser(String email) {
    const premiumTestUsers = [
      '<EMAIL>',
      '<EMAIL>',
    ];
    return premiumTestUsers.contains(email.toLowerCase());
  }

  // Check if user has premium access
  static Future<bool> hasAccessToPremiumContent() async {
    // Check if current user is a premium test user
    final user = await AuthService.getStoredUser();
    if (user != null && _isPremiumTestUser(user.email)) {
      print('🧪 Premium test user detected - granting premium access for: ${user.email}');
      return true;
    }

    print('🔍 Checking subscription status for premium access...');
    final status = await getSubscriptionStatus();

    if (!status.success) {
      print('❌ Subscription status API call failed: ${status.message}');
      return false;
    }

    if (status.data == null) {
      print('❌ No subscription data received');
      return false;
    }

    final hasSubscription = status.data!.hasSubscription;
    final isActive = status.data!.isActive;

    print('📊 Subscription check results:');
    print('   - hasSubscription: $hasSubscription');
    print('   - isActive: $isActive');
    print('   - Combined access: ${hasSubscription && isActive}');

    // User has premium access if they have an active subscription
    final hasAccess = hasSubscription && isActive;

    if (hasAccess) {
      print('✅ User has premium access');
    } else {
      print('❌ User does not have premium access');
    }

    return hasAccess;
  }
}
