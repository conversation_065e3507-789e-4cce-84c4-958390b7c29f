class Topic {
  final String id;
  final String name;
  final String description;
  final String? thumbnail;
  final String categoryId;
  final int order;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  Topic({
    required this.id,
    required this.name,
    required this.description,
    this.thumbnail,
    required this.categoryId,
    required this.order,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Topic.fromJson(Map<String, dynamic> json) {
    // Handle both 'title' and 'name' fields for backward compatibility
    final name = json['title'] ?? json['name'] ?? '';

    // Handle nested category object or direct categoryId
    String categoryId = '';
    if (json['category'] != null && json['category'] is Map) {
      categoryId = json['category']['_id'] ?? json['category']['id'] ?? '';
    } else {
      categoryId = json['categoryId'] ?? '';
    }

    return Topic(
      id: json['_id'] ?? json['id'] ?? '',
      name: name,
      description: json['description'] ?? '',
      thumbnail: json['thumbnail'],
      categoryId: categoryId,
      order: json['order'] ?? 0,
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'description': description,
      'thumbnail': thumbnail,
      'categoryId': categoryId,
      'order': order,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

class TopicsResponse {
  final bool success;
  final String message;
  final List<Topic>? data;

  TopicsResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory TopicsResponse.fromJson(Map<String, dynamic> json) {
    return TopicsResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null
          ? (json['data'] as List).map((item) => Topic.fromJson(item)).toList()
          : null,
    );
  }
}
