import 'package:bolo_app/utils/premium_content_helper.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import '../models/mcq_models.dart';
import '../../../models/content_progress.dart';
import '../../../models/enhanced_user_stats.dart';
import '../services/mcq_service.dart';
import '../services/progress_service.dart' as learning_progress_service;
import '../../../services/enhanced_user_stats_service.dart';


class MCQViewerScreen extends StatefulWidget {
  final String mcqId;
  final String title;
  final VoidCallback? onProgressUpdate;

  const MCQViewerScreen({
    super.key,
    required this.mcqId,
    required this.title,
    this.onProgressUpdate,
  });

  @override
  State<MCQViewerScreen> createState() => _MCQViewerScreenState();
}

class _MCQViewerScreenState extends State<MCQViewerScreen> {
  MCQData? _mcqData;
  bool _isLoading = true;
  int _currentQuestionIndex = 0;
  Map<String, String> _selectedAnswers = {};
  Timer? _timer;
  int _timeElapsed = 0;
  bool _isSubmitting = false;
  bool _showResults = false;
  MCQResult? _result;

  @override
  void initState() {
    super.initState();
    _checkAccessAndLoadData();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _timeElapsed++;
      });
    });
  }

  Future<void> _checkAccessAndLoadData() async {
    try {
      // First load the MCQ data to check if it's premium
      final mcqData = await MCQService.getMCQById(widget.mcqId);

      if (mcqData != null && mcqData.isPremium) {
        // Check premium access before proceeding
        if (mounted) {
          final hasAccess = await PremiumContentHelper.handleMCQAccess(
            context: context,
            isPremium: true,
            mcqTitle: widget.title,
            popCurrentScreen: true, // Pop the MCQ viewer if no access
          );

          if (!hasAccess) {
            return; // User was redirected to subscription screen
          }
        }
      }

      // If we reach here, user has access or MCQ is not premium
      setState(() {
        _mcqData = mcqData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading quiz: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadMCQData() async {
    try {
      final mcqData = await MCQService.getMCQById(widget.mcqId);
      setState(() {
        _mcqData = mcqData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading quiz: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _selectAnswer(String questionId, String optionId) {
    setState(() {
      _selectedAnswers[questionId] = optionId;
    });
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < (_mcqData?.questions.length ?? 0) - 1) {
      setState(() {
        _currentQuestionIndex++;
      });
    }
  }

  void _previousQuestion() {
    if (_currentQuestionIndex > 0) {
      setState(() {
        _currentQuestionIndex--;
      });
    }
  }

  Future<void> _submitQuiz() async {
    if (_mcqData == null) return;

    setState(() {
      _isSubmitting = true;
    });

    _timer?.cancel();

    // Create answers list
    final answers = <MCQAnswer>[];
    for (final question in _mcqData!.questions) {
      final selectedOptionId = _selectedAnswers[question.id] ?? '';
      final isCorrect = MCQService.validateAnswer(question, selectedOptionId);

      answers.add(MCQAnswer(
        questionId: question.id,
        selectedOptionId: selectedOptionId,
        isCorrect: isCorrect,
        answeredAt: DateTime.now(),
      ));
    }

    // Calculate results
    final result = MCQService.calculateResults(
      mcqId: widget.mcqId,
      mcqData: _mcqData!,
      answers: answers,
      timeTaken: _timeElapsed,
    );

    // Submit to backend (optional)
    await MCQService.submitMCQAnswers(
      mcqId: widget.mcqId,
      answers: answers,
      timeTaken: _timeElapsed,
    );

    // Record progress
    await learning_progress_service.ProgressService.recordContentProgress(
      contentId: widget.mcqId,
      contentType: ContentType.mcq,
      progressPercentage: 100.0,
      timeSpent: _timeElapsed,
      metadata: {
        'score': result.score,
        'totalQuestions': result.totalQuestions,
        'percentage': result.percentage,
        'passed': result.passed,
        'attempts': 1, // Could be tracked separately
      },
    );

    setState(() {
      _result = result;
      _showResults = true;
      _isSubmitting = false;
    });

    // Update user statistics for test completion
    if (result.passed) {
      await EnhancedUserStatsService.recordTestPassed(
        testId: widget.mcqId,
        testType: 'mcq',
        score: result.percentage,
        timeSpent: _timeElapsed,
      );
    }

    // Notify parent widget of progress update if callback provided
    if (widget.onProgressUpdate != null) {
      widget.onProgressUpdate!();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: const Color(0xFF2D1B69),
        foregroundColor: Colors.white,
        title: Text(
          widget.title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          if (!_showResults)
            Container(
              margin: const EdgeInsets.only(right: 16),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFF6C5CE7),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                MCQService.formatTime(_timeElapsed),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF6C5CE7),
              ),
            )
          : _mcqData == null
              ? _buildErrorState()
              : _showResults
                  ? _buildResultsView()
                  : _buildQuizView(),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load quiz',
            style: TextStyle(
              color: Colors.grey.withValues(alpha: 0.7),
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              _loadMCQData();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6C5CE7),
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildQuizView() {
    final question = _mcqData!.questions[_currentQuestionIndex];
    final selectedAnswer = _selectedAnswers[question.id];

    return Column(
      children: [
        // Progress indicator
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A3E),
            border: Border(
              bottom: BorderSide(
                color: Colors.white.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'Question ${_currentQuestionIndex + 1} of ${_mcqData!.questions.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: MCQService.getDifficultyColor(question.difficulty),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          MCQService.getDifficultyIcon(question.difficulty),
                          size: 12,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          question.difficulty.toUpperCase(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: (_currentQuestionIndex + 1) / _mcqData!.questions.length,
                backgroundColor: Colors.grey.withValues(alpha: 0.3),
                valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF6C5CE7)),
              ),
            ],
          ),
        ),

        // Question content
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Question text
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2A2A3E),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.1),
                    ),
                  ),
                  child: Text(
                    question.questionText,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      height: 1.4,
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Options
                ...question.options.asMap().entries.map((entry) {
                  final index = entry.key;
                  final option = entry.value;
                  final isSelected = selectedAnswer == option.id;

                  return Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: GestureDetector(
                      onTap: () => _selectAnswer(question.id, option.id),
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? const Color(0xFF6C5CE7).withValues(alpha: 0.2)
                              : const Color(0xFF2A2A3E),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelected
                                ? const Color(0xFF6C5CE7)
                                : Colors.white.withValues(alpha: 0.1),
                            width: isSelected ? 2 : 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Container(
                              width: 24,
                              height: 24,
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? const Color(0xFF6C5CE7)
                                    : Colors.transparent,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: isSelected
                                      ? const Color(0xFF6C5CE7)
                                      : Colors.grey,
                                  width: 2,
                                ),
                              ),
                              child: isSelected
                                  ? const Icon(
                                      Icons.check,
                                      size: 16,
                                      color: Colors.white,
                                    )
                                  : null,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                '${String.fromCharCode(65 + index)}. ${option.text}',
                                style: TextStyle(
                                  color: isSelected ? Colors.white : Colors.white70,
                                  fontSize: 16,
                                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }),
              ],
            ),
          ),
        ),

        // Navigation buttons
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A3E),
            border: Border(
              top: BorderSide(
                color: Colors.white.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              // Previous button
              if (_currentQuestionIndex > 0)
                Expanded(
                  child: ElevatedButton(
                    onPressed: _previousQuestion,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
                      foregroundColor: const Color(0xFF6C5CE7),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('Previous'),
                  ),
                ),
              if (_currentQuestionIndex > 0) const SizedBox(width: 16),

              // Next/Submit button
              Expanded(
                child: ElevatedButton(
                  onPressed: _isSubmitting
                      ? null
                      : _currentQuestionIndex < _mcqData!.questions.length - 1
                          ? _nextQuestion
                          : _submitQuiz,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6C5CE7),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: _isSubmitting
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : Text(
                          _currentQuestionIndex < _mcqData!.questions.length - 1
                              ? 'Next'
                              : 'Submit Quiz',
                        ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildResultsView() {
    if (_result == null) return const SizedBox();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Results header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: _result!.passed
                  ? const Color(0xFF4CAF50).withValues(alpha: 0.2)
                  : const Color(0xFFF44336).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: _result!.passed
                    ? const Color(0xFF4CAF50)
                    : const Color(0xFFF44336),
                width: 2,
              ),
            ),
            child: Column(
              children: [
                Icon(
                  _result!.passed ? Icons.check_circle : Icons.cancel,
                  size: 64,
                  color: _result!.passed
                      ? const Color(0xFF4CAF50)
                      : const Color(0xFFF44336),
                ),
                const SizedBox(height: 16),
                Text(
                  _result!.passed ? 'Congratulations!' : 'Keep Trying!',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _result!.passed
                      ? 'You passed the quiz!'
                      : 'You need ${_mcqData!.passingScore}% to pass',
                  style: TextStyle(
                    color: Colors.grey.withValues(alpha: 0.8),
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Score details
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xFF2A2A3E),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.1),
              ),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Your Score',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      '${_result!.percentage.toStringAsFixed(1)}%',
                      style: TextStyle(
                        color: _result!.passed
                            ? const Color(0xFF4CAF50)
                            : const Color(0xFFF44336),
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        'Correct',
                        '${_result!.score}',
                        const Color(0xFF4CAF50),
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        'Total',
                        '${_result!.totalQuestions}',
                        const Color(0xFF6C5CE7),
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        'Time',
                        MCQService.formatTime(_result!.timeTaken),
                        const Color(0xFFFF9800),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
                    foregroundColor: const Color(0xFF6C5CE7),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text('Back to Module'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    // Reset quiz for retake
                    setState(() {
                      _currentQuestionIndex = 0;
                      _selectedAnswers.clear();
                      _timeElapsed = 0;
                      _showResults = false;
                      _result = null;
                    });
                    _startTimer();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6C5CE7),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text('Retake Quiz'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey.withValues(alpha: 0.7),
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}