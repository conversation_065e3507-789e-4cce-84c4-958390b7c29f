import 'package:flutter/material.dart';
import '../services/text_content_service.dart';

class TextContentTestScreen extends StatefulWidget {
  const TextContentTestScreen({super.key});

  @override
  State<TextContentTestScreen> createState() => _TextContentTestScreenState();
}

class _TextContentTestScreenState extends State<TextContentTestScreen> {
  bool _isLoading = false;
  String _result = '';
  
  // Test content IDs that we know exist
  final List<String> _testContentIds = [
    '687e8cb9822a95065d577063', // Present Tense Summary
    '687e8cb9822a95065d577068', // Daily Routine Reading
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: const Color(0xFF2D1B69),
        foregroundColor: Colors.white,
        title: const Text('Text Content Test'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Text Content Service Test',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            const Text(
              'Test Content IDs:',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 10),
            
            ..._testContentIds.map((id) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      id,
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 12,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: _isLoading ? null : () => _testTextContent(id),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF6C5CE7),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    ),
                    child: const Text('Test', style: TextStyle(fontSize: 12)),
                  ),
                ],
              ),
            )),
            
            const SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _testAllContent,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4CAF50),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
              child: _isLoading 
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Text('Test All Content'),
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              'Results:',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 10),
            
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFF2A2A3E),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.1),
                  ),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _result.isEmpty ? 'No results yet. Click "Test All Content" to start.' : _result,
                    style: TextStyle(
                      color: _result.isEmpty ? Colors.grey : Colors.white,
                      fontSize: 12,
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testTextContent(String contentId) async {
    setState(() {
      _isLoading = true;
      _result = 'Testing content ID: $contentId...\n';
    });

    try {
      final textContent = await TextContentService.getTextContentById(contentId);
      
      if (textContent != null) {
        setState(() {
          _result += '✅ SUCCESS: Content loaded successfully!\n';
          _result += 'Title: ${textContent.title}\n';
          _result += 'Type: ${textContent.contentType}\n';
          _result += 'Format: ${textContent.contentFormat}\n';
          _result += 'Has Access: ${textContent.hasAccess}\n';
          _result += 'Content Preview: ${textContent.content.substring(0, 100)}...\n';
          _result += '---\n';
        });
      } else {
        setState(() {
          _result += '❌ FAILED: Content returned null\n';
          _result += '---\n';
        });
      }
    } catch (e) {
      setState(() {
        _result += '❌ ERROR: $e\n';
        _result += '---\n';
      });
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testAllContent() async {
    setState(() {
      _isLoading = true;
      _result = 'Starting comprehensive text content test...\n\n';
    });

    for (int i = 0; i < _testContentIds.length; i++) {
      final contentId = _testContentIds[i];
      setState(() {
        _result += 'Testing ${i + 1}/${_testContentIds.length}: $contentId\n';
      });

      try {
        final textContent = await TextContentService.getTextContentById(contentId);
        
        if (textContent != null) {
          setState(() {
            _result += '✅ SUCCESS!\n';
            _result += '  Title: ${textContent.title}\n';
            _result += '  Type: ${textContent.contentType}\n';
            _result += '  Format: ${textContent.contentFormat}\n';
            _result += '  Has Access: ${textContent.hasAccess}\n';
            _result += '  Word Count: ${textContent.wordCount}\n';
            _result += '  Reading Time: ${textContent.estimatedReadingTime} min\n';
            _result += '  Content Length: ${textContent.content.length} chars\n';
          });
        } else {
          setState(() {
            _result += '❌ FAILED: Returned null\n';
          });
        }
      } catch (e) {
        setState(() {
          _result += '❌ ERROR: $e\n';
        });
      }

      setState(() {
        _result += '\n';
      });

      // Small delay between requests
      await Future.delayed(const Duration(milliseconds: 500));
    }

    setState(() {
      _result += '🎉 Test completed!\n';
      _isLoading = false;
    });
  }
}
