import 'package:bolo_app/utils/premium_content_helper.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import '../models/questionnaire_models.dart';
import '../../../models/content_progress.dart';
import '../services/questionnaire_service.dart';
import '../services/progress_service.dart' as learning_progress_service;
import '../../../services/enhanced_user_stats_service.dart';

import 'questionnaire_results_screen.dart';

class QuestionnaireViewerScreen extends StatefulWidget {
  final String questionnaireId;
  final String title;

  const QuestionnaireViewerScreen({
    super.key,
    required this.questionnaireId,
    required this.title,
  });

  @override
  State<QuestionnaireViewerScreen> createState() => _QuestionnaireViewerScreenState();
}

class _QuestionnaireViewerScreenState extends State<QuestionnaireViewerScreen> {
  QuestionnaireData? _questionnaireData;
  bool _isLoading = true;
  int _currentQuestionIndex = 0;
  Map<String, dynamic> _selectedAnswers = {};
  Map<String, TextEditingController> _textControllers = {};
  Timer? _timer;
  int _timeElapsed = 0;
  bool _isSubmitting = false;
  bool _showResults = false;
  QuestionnaireResult? _result;
  QuestionnaireSubmissionResult? _submissionResult;

  @override
  void initState() {
    super.initState();
    _checkAccessAndLoadData();
  }

  @override
  void dispose() {
    _timer?.cancel();
    // Dispose all text controllers
    for (final controller in _textControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _checkAccessAndLoadData() async {
    try {
      // First load the questionnaire data to check if it's premium
      final questionnaireData = await QuestionnaireService.getQuestionnaireById(widget.questionnaireId);

      if (questionnaireData != null && questionnaireData.isPremium) {
        // Check premium access using the centralized helper
        if (mounted) {
          final hasAccess = await PremiumContentHelper.handleQuestionnaireAccess(
            context: context,
            isPremium: true,
            questionnaireTitle: widget.title,
            popCurrentScreen: true, // Pop the questionnaire viewer if no access
          );

          if (!hasAccess) {
            return; // User was redirected to subscription screen
          }
        }
      }

      // If we reach here, user has access or questionnaire is not premium
      await _loadQuestionnaireData();
    } catch (e) {
      print('💥 Error checking access: $e');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading questionnaire: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _timeElapsed++;
      });
    });
  }

  Future<void> _loadQuestionnaireData() async {
    try {
      final questionnaireData = await QuestionnaireService.getQuestionnaireById(widget.questionnaireId);
      setState(() {
        _questionnaireData = questionnaireData;
        _isLoading = false;
      });

      if (questionnaireData != null) {
        // Initialize text controllers for text-based questions
        _initializeTextControllers();
        // Record view and start timer
        QuestionnaireService.recordQuestionnaireView(widget.questionnaireId);
        _startTimer();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading questionnaire: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _initializeTextControllers() {
    if (_questionnaireData == null) return;

    for (final question in _questionnaireData!.questions) {
      if (question.questionType == QuestionType.textInput ||
          question.questionType == QuestionType.shortAnswer) {
        if (!_textControllers.containsKey(question.id)) {
          final controller = TextEditingController(
            text: _selectedAnswers[question.id]?.toString() ?? ''
          );
          controller.addListener(() {
            _selectAnswer(question.id, controller.text);
          });
          _textControllers[question.id] = controller;
        }
      }
    }
  }

  void _selectAnswer(String questionId, dynamic answer) {
    setState(() {
      _selectedAnswers[questionId] = answer;
    });
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < (_questionnaireData?.questions.length ?? 0) - 1) {
      setState(() {
        _currentQuestionIndex++;
      });
    }
  }

  void _previousQuestion() {
    if (_currentQuestionIndex > 0) {
      setState(() {
        _currentQuestionIndex--;
      });
    }
  }

  Future<void> _submitQuestionnaire() async {
    if (_questionnaireData == null) return;

    setState(() {
      _isSubmitting = true;
    });

    _timer?.cancel();

    // Prepare answers for new API format
    final userAnswers = <int, dynamic>{};
    final timeSpent = <int, int>{};

    for (int i = 0; i < _questionnaireData!.questions.length; i++) {
      final question = _questionnaireData!.questions[i];
      final userAnswer = _selectedAnswers[question.id];

      if (userAnswer != null) {
        userAnswers[i] = userAnswer;
      }

      // For now, distribute time evenly across questions
      // In a more sophisticated implementation, you could track time per question
      timeSpent[i] = (_timeElapsed / _questionnaireData!.questions.length).round();
    }

    // Submit to backend with new API
    final submissionResult = await QuestionnaireService.submitQuestionnaireAnswers(
      questionnaireId: widget.questionnaireId,
      userAnswers: userAnswers,
      timeSpent: timeSpent,
    );

    if (submissionResult != null) {
      // Record progress
      await learning_progress_service.ProgressService.recordContentProgress(
        contentId: widget.questionnaireId,
        contentType: ContentType.questionnaire,
        progressPercentage: 100.0,
        timeSpent: _timeElapsed,
      );

      // Update user statistics for questionnaire completion
      await EnhancedUserStatsService.recordContentCompleted(
        contentId: widget.questionnaireId,
        contentType: 'questionnaire',
        timeSpent: _timeElapsed,
        score: submissionResult.score.toDouble(),
      );

      // Use the result from the backend
      setState(() {
        _submissionResult = submissionResult;
        _showResults = true;
        _isSubmitting = false;
      });
    } else {
      // Handle submission failure
      setState(() {
        _isSubmitting = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to submit questionnaire. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.title,
          style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        actions: [
          if (!_showResults && _questionnaireData != null)
            Container(
              margin: const EdgeInsets.only(right: 16),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFF6C5CE7),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                QuestionnaireService.formatTime(_timeElapsed),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: _isLoading ? _buildLoadingWidget() : _buildContent(),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Color(0xFF6C5CE7)),
          SizedBox(height: 16),
          Text(
            'Loading questionnaire...',
            style: TextStyle(color: Colors.white70),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_questionnaireData == null) {
      return _buildErrorWidget();
    }

    if (_showResults) {
      return _buildResultsWidget();
    }

    if (_questionnaireData!.questions.isEmpty) {
      return _buildEmptyWidget();
    }

    return _buildQuestionnaireWidget();
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 64),
          const SizedBox(height: 16),
          const Text(
            'Failed to load questionnaire',
            style: TextStyle(color: Colors.white70, fontSize: 18),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _loadQuestionnaireData(),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6C5CE7),
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.quiz_outlined, color: Colors.white70, size: 64),
          SizedBox(height: 16),
          Text(
            'No questions available',
            style: TextStyle(color: Colors.white70, fontSize: 18),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionnaireWidget() {
    final question = _questionnaireData!.questions[_currentQuestionIndex];
    final selectedAnswer = _selectedAnswers[question.id];

    return Column(
      children: [
        // Progress indicator
        Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Question ${_currentQuestionIndex + 1} of ${_questionnaireData!.questions.length}',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    '${QuestionnaireService.getDifficultyIcon(question.difficulty)} ${question.difficulty.toUpperCase()}',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              LinearProgressIndicator(
                value: (_currentQuestionIndex + 1) / _questionnaireData!.questions.length,
                backgroundColor: Colors.white.withValues(alpha: 0.2),
                valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF6C5CE7)),
              ),
            ],
          ),
        ),

        // Question content
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Question text
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2A2A3E),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.1),
                    ),
                  ),
                  child: Text(
                    question.questionText,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      height: 1.4,
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Answer input based on question type
                _buildAnswerInput(question, selectedAnswer),

                const SizedBox(height: 24),

                // Hints (if available)
                if (question.hints.isNotEmpty) ...[
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2A2A3E).withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.orange.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.lightbulb_outline,
                              color: Colors.orange.withValues(alpha: 0.8),
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Hint',
                              style: TextStyle(
                                color: Colors.orange.withValues(alpha: 0.8),
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        ...question.hints.map((hint) => Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Text(
                            '• $hint',
                            style: TextStyle(
                              color: Colors.orange.withValues(alpha: 0.7),
                              fontSize: 14,
                            ),
                          ),
                        )),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                ],
              ],
            ),
          ),
        ),

        // Navigation buttons
        Container(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              if (_currentQuestionIndex > 0)
                Expanded(
                  child: ElevatedButton(
                    onPressed: _previousQuestion,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2A2A3E),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('Previous'),
                  ),
                ),
              if (_currentQuestionIndex > 0) const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: _isSubmitting ? null : () {
                    if (_currentQuestionIndex < _questionnaireData!.questions.length - 1) {
                      _nextQuestion();
                    } else {
                      _showSubmitDialog();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6C5CE7),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isSubmitting
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Text(
                          _currentQuestionIndex < _questionnaireData!.questions.length - 1
                              ? 'Next'
                              : 'Submit',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showSubmitDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2A2A3E),
        title: const Text(
          'Submit Questionnaire?',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Are you sure you want to submit your answers?',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 16),
            Text(
              'Answered: ${_selectedAnswers.length} of ${_questionnaireData!.questions.length} questions',
              style: const TextStyle(color: Colors.white70, fontSize: 14),
            ),
            Text(
              'Time taken: ${QuestionnaireService.formatTime(_timeElapsed)}',
              style: const TextStyle(color: Colors.white70, fontSize: 14),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel', style: TextStyle(color: Colors.white70)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _submitQuestionnaire();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6C5CE7),
              foregroundColor: Colors.white,
            ),
            child: const Text('Submit'),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsWidget() {
    if (_submissionResult == null) return const SizedBox.shrink();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Results header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: _submissionResult!.passed
                    ? [const Color(0xFF4CAF50), const Color(0xFF66BB6A)]
                    : [const Color(0xFFF44336), const Color(0xFFEF5350)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              children: [
                Icon(
                  _submissionResult!.passed ? Icons.check_circle : Icons.cancel,
                  color: Colors.white,
                  size: 64,
                ),
                const SizedBox(height: 16),
                Text(
                  _submissionResult!.passed ? 'Congratulations!' : 'Keep Trying!',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _submissionResult!.feedback.isNotEmpty
                      ? _submissionResult!.feedback
                      : (_submissionResult!.passed
                          ? 'You passed the questionnaire!'
                          : 'You can try again to improve your score.'),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Score details
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xFF2A2A3E),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.1),
              ),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Your Score',
                      style: TextStyle(color: Colors.white70, fontSize: 16),
                    ),
                    Text(
                      '${_submissionResult!.score}%',
                      style: TextStyle(
                        color: _submissionResult!.passed ? Colors.green : Colors.red,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Correct Answers',
                      style: TextStyle(color: Colors.white70, fontSize: 14),
                    ),
                    Text(
                      '${_submissionResult!.correctAnswers}/${_submissionResult!.totalQuestions}',
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Time Taken',
                      style: TextStyle(color: Colors.white70, fontSize: 14),
                    ),
                    Text(
                      QuestionnaireService.formatTime(_submissionResult!.completionTime),
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Passing Score',
                      style: TextStyle(color: Colors.white70, fontSize: 14),
                    ),
                    Text(
                      '${_questionnaireData!.passingScore}%',
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Action buttons
          Column(
            children: [
              // First row - View Details button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => QuestionnaireResultsScreen(
                          questionnaireId: widget.questionnaireId,
                          title: widget.title,
                        ),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4CAF50),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text('View Detailed Results'),
                ),
              ),
              const SizedBox(height: 16),
              // Second row - Back and Try Again buttons
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2A2A3E),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('Back to Module'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        // Reset and try again
                        setState(() {
                          _currentQuestionIndex = 0;
                          _selectedAnswers.clear();
                          _timeElapsed = 0;
                          _showResults = false;
                          _result = null;
                          _submissionResult = null;
                        });
                        // Clear all text controllers
                        for (final controller in _textControllers.values) {
                          controller.clear();
                        }
                        _startTimer();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF6C5CE7),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('Try Again'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnswerInput(QuestionnaireQuestion question, dynamic selectedAnswer) {
    switch (question.questionType) {
      case QuestionType.multipleChoice:
        return _buildMultipleChoiceInput(question, selectedAnswer);
      case QuestionType.textInput:
      case QuestionType.shortAnswer:
        return _buildTextInput(question, selectedAnswer);
      case QuestionType.trueFalse:
        return _buildTrueFalseInput(question, selectedAnswer);
    }
  }

  Widget _buildMultipleChoiceInput(QuestionnaireQuestion question, dynamic selectedAnswer) {
    return Column(
      children: question.options.asMap().entries.map((entry) {
        final index = entry.key;
        final option = entry.value;
        final isSelected = selectedAnswer == index;

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: InkWell(
            onTap: () => _selectAnswer(question.id, index),
            borderRadius: BorderRadius.circular(12),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isSelected
                    ? const Color(0xFF6C5CE7).withValues(alpha: 0.2)
                    : const Color(0xFF2A2A3E),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isSelected
                      ? const Color(0xFF6C5CE7)
                      : Colors.white.withValues(alpha: 0.1),
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected
                          ? const Color(0xFF6C5CE7)
                          : Colors.transparent,
                      border: Border.all(
                        color: isSelected
                            ? const Color(0xFF6C5CE7)
                            : Colors.white.withValues(alpha: 0.3),
                        width: 2,
                      ),
                    ),
                    child: isSelected
                        ? const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          )
                        : null,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      option,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.white70,
                        fontSize: 16,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildTextInput(QuestionnaireQuestion question, dynamic selectedAnswer) {
    // Get or create a persistent controller for this question
    if (!_textControllers.containsKey(question.id)) {
      _textControllers[question.id] = TextEditingController(
        text: selectedAnswer?.toString() ?? ''
      );
      // Add listener to update answers when text changes
      _textControllers[question.id]!.addListener(() {
        _selectAnswer(question.id, _textControllers[question.id]!.text);
      });
    } else {
      // Update controller text if answer was set programmatically
      if (_textControllers[question.id]!.text != (selectedAnswer?.toString() ?? '')) {
        _textControllers[question.id]!.text = selectedAnswer?.toString() ?? '';
      }
    }

    final controller = _textControllers[question.id]!;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A3E),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            question.questionType == QuestionType.textInput ? 'Your Answer:' : 'Short Answer:',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: controller,
            maxLines: question.questionType == QuestionType.textInput ? 4 : 1,
            maxLength: question.maxLength,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
            decoration: InputDecoration(
              hintText: question.questionType == QuestionType.textInput
                  ? 'Type your detailed answer here...'
                  : 'Type your answer...',
              hintStyle: TextStyle(
                color: Colors.white.withValues(alpha: 0.5),
                fontSize: 16,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: Colors.white.withValues(alpha: 0.2),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: Colors.white.withValues(alpha: 0.2),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: Color(0xFF6C5CE7),
                  width: 2,
                ),
              ),
              filled: true,
              fillColor: const Color(0xFF1A1A2E),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrueFalseInput(QuestionnaireQuestion question, dynamic selectedAnswer) {
    return Column(
      children: [
        // True option
        Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: InkWell(
            onTap: () => _selectAnswer(question.id, true),
            borderRadius: BorderRadius.circular(12),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: selectedAnswer == true
                    ? const Color(0xFF4CAF50).withValues(alpha: 0.2)
                    : const Color(0xFF2A2A3E),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: selectedAnswer == true
                      ? const Color(0xFF4CAF50)
                      : Colors.white.withValues(alpha: 0.1),
                  width: selectedAnswer == true ? 2 : 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: selectedAnswer == true
                          ? const Color(0xFF4CAF50)
                          : Colors.transparent,
                      border: Border.all(
                        color: selectedAnswer == true
                            ? const Color(0xFF4CAF50)
                            : Colors.white.withValues(alpha: 0.3),
                        width: 2,
                      ),
                    ),
                    child: selectedAnswer == true
                        ? const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          )
                        : null,
                  ),
                  const SizedBox(width: 16),
                  const Expanded(
                    child: Text(
                      'True',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        // False option
        InkWell(
            onTap: () => _selectAnswer(question.id, false),
            borderRadius: BorderRadius.circular(12),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: selectedAnswer == false
                    ? const Color(0xFFF44336).withValues(alpha: 0.2)
                    : const Color(0xFF2A2A3E),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: selectedAnswer == false
                      ? const Color(0xFFF44336)
                      : Colors.white.withValues(alpha: 0.1),
                  width: selectedAnswer == false ? 2 : 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: selectedAnswer == false
                          ? const Color(0xFFF44336)
                          : Colors.transparent,
                      border: Border.all(
                        color: selectedAnswer == false
                            ? const Color(0xFFF44336)
                            : Colors.white.withValues(alpha: 0.3),
                        width: 2,
                      ),
                    ),
                    child: selectedAnswer == false
                        ? const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 16,
                          )
                        : null,
                  ),
                  const SizedBox(width: 16),
                  const Expanded(
                    child: Text(
                      'False',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
