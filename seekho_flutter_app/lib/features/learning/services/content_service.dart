import 'dart:convert';
import 'package:bolo_app/models/category_complete.dart';

import '../../../models/category.dart';
import '../../../models/topic.dart';
import '../../../models/video.dart';

import '../../../utils/constants.dart';
import '../../../services/auth_service.dart';
import '../../../services/http_client_service.dart';

class ContentService {
  // Get all categories
  static Future<CategoriesResponse> getCategories() async {
    try {
      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.categories}'),
      );

      return CategoriesResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error fetching categories: $e');
      return CategoriesResponse(
        success: false,
        message: 'Failed to fetch categories: $e',
      );
    }
  }

  // Get category by ID
  static Future<Category?> getCategoryById(String categoryId) async {
    try {
      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.categories}/$categoryId'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] && data['data'] != null) {
          return Category.fromJson(data['data']);
        }
      }
      return null;
    } catch (e) {
      print('Error fetching category: $e');
      return null;
    }
  }

  // Get topics in a category
  static Future<TopicsResponse> getTopicsInCategory(String categoryId) async {
    try {
      final url = '${ApiConstants.baseUrl}${ApiConstants.categories}/$categoryId/topics';

      final response = await HttpClientService.get(
        Uri.parse(url),
      );

      final decodedResponse = json.decode(response.body);
      return TopicsResponse.fromJson(decodedResponse);
    } catch (e) {
      return TopicsResponse(
        success: false,
        message: 'Failed to fetch topics: $e',
      );
    }
  }

  // Get all topics
  static Future<TopicsResponse> getAllTopics() async {
    try {
      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.topics}'),
      );

      return TopicsResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error fetching topics: $e');
      return TopicsResponse(
        success: false,
        message: 'Failed to fetch topics: $e',
      );
    }
  }

  // Get videos in a topic
  static Future<VideosResponse> getVideosInTopic(String topicId) async {
    try {
      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.topics}/$topicId/videos'),
      );

      return VideosResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error fetching videos: $e');
      return VideosResponse(
        success: false,
        message: 'Failed to fetch videos: $e',
      );
    }
  }

  // Get all videos
  static Future<VideosResponse> getAllVideos() async {
    try {
      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videos}'),
      );

      print('🎬 Raw videos API response: ${response.body}');

      final videosResponse = VideosResponse.fromJson(json.decode(response.body));

      // Debug: Print premium status of each video
      if (videosResponse.data != null) {
        print('📊 Videos loaded: ${videosResponse.data!.length}');
        for (var video in videosResponse.data!) {
          print('🎥 Video: ${video.title} - Premium: ${video.isPremium}');
        }
      }

      return videosResponse;
    } catch (e) {
      print('Error fetching videos: $e');
      return VideosResponse(
        success: false,
        message: 'Failed to fetch videos: $e',
      );
    }
  }

  // Get videos by category
  static Future<VideosResponse> getVideosByCategory(String categoryId) async {
    try {
      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.categories}/$categoryId/videos'),
      );

      return VideosResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error fetching videos by category: $e');
      return VideosResponse(
        success: false,
        message: 'Failed to fetch videos: $e',
      );
    }
  }

  // Get video by ID
  static Future<Video?> getVideoById(String videoId) async {
    try {
      final token = await AuthService.getAccessToken();
      final headers = <String, String>{};
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }

      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videos}/$videoId'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] && data['data'] != null) {
          return Video.fromJson(data['data']);
        }
      }
      return null;
    } catch (e) {
      print('Error fetching video: $e');
      return null;
    }
  }

  // Search videos
  static Future<VideosResponse> searchVideos(String query, {int page = 1, int limit = 10}) async {
    try {
      final uri = Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videos}/search').replace(
        queryParameters: {
          'q': query,
          'page': page.toString(),
          'limit': limit.toString(),
        },
      );

      final response = await HttpClientService.get(
        uri,
      );

      return VideosResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error searching videos: $e');
      return VideosResponse(
        success: false,
        message: 'Failed to search videos: $e',
      );
    }
  }

  // Record video view
  static Future<bool> recordVideoView(String videoId) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) return false;

      final response = await HttpClientService.post(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videos}/$videoId/view'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error recording video view: $e');
      return false;
    }
  }

  // Get complete category data (topics, videos, stats)
  static Future<CategoryCompleteResponse> getCategoryComplete(String categoryId) async {
    try {
      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.categories}/$categoryId/complete'),
      );

      return CategoryCompleteResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error fetching complete category data: $e');
      return CategoryCompleteResponse(
        success: false,
        message: 'Failed to fetch category data: $e',
      );
    }
  }

  // Get popular videos
  static Future<VideosResponse> getPopularVideos({int limit = 10}) async {
    try {
      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videosPopular}?limit=$limit'),
      );

      return VideosResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error fetching popular videos: $e');
      return VideosResponse(
        success: false,
        message: 'Failed to fetch popular videos: $e',
      );
    }
  }

  // Get new videos
  static Future<VideosResponse> getNewVideos({int limit = 20}) async {
    try {
      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videosNew}?limit=$limit'),
      );

      return VideosResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error fetching new videos: $e');
      return VideosResponse(
        success: false,
        message: 'Failed to fetch new videos: $e',
      );
    }
  }

  // Get video stream URL
  static Future<Map<String, dynamic>?> getVideoStreamUrl(String videoId) async {
    try {
      final token = await AuthService.getAccessToken();
      final headers = {'Content-Type': 'application/json'};

      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }

      print('🔗 Making request to: ${ApiConstants.baseUrl}${ApiConstants.videosStream}/$videoId/stream');
      print('🔑 Headers: $headers');

      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videosStream}/$videoId/stream'),
        headers: headers,
      );

      print('📡 Response status: ${response.statusCode}');
      print('📄 Response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        print('📊 Parsed response data: $data');

        if (data['success']) {
          print('✅ Stream data found: ${data['data']}');
          return data['data'];
        } else {
          print('❌ Backend returned success: false. Message: ${data['message']}');
        }
      } else {
        print('❌ HTTP Error ${response.statusCode}: ${response.body}');
      }
      return null;
    } catch (e) {
      print('❌ Exception fetching video stream URL: $e');
      print('📍 Stack trace: ${StackTrace.current}');
      return null;
    }
  }

  // Record video progress
  static Future<bool> recordVideoProgress(
    String videoId, {
    required int progress,
    required int duration,
    required bool completed,
  }) async {
    try {
      final token = await AuthService.getAccessToken();
      if (token == null) return false;

      final response = await HttpClientService.post(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videosProgress}/$videoId/progress'),
        headers: {
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'progress': progress,
          'duration': duration,
          'completed': completed,
          'deviceType': 'mobile',
        }),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error recording video progress: $e');
      return false;
    }
  }

  // Get related videos
  static Future<VideosResponse> getRelatedVideos(String videoId, {int limit = 5}) async {
    try {
      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.videosRelated}/$videoId/related?limit=$limit'),
      );

      return VideosResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error fetching related videos: $e');
      return VideosResponse(
        success: false,
        message: 'Failed to fetch related videos: $e',
      );
    }
  }
}
