import 'dart:convert';
import 'package:bolo_app/utils/constants.dart';
import 'package:http/http.dart' as HttpClientService;

import '../models/learning_module.dart';


class LearningModuleService {
  // Get modules by class level
  static Future<LearningModulesResponse> getModulesByClass(int classLevel) async {
    try {
      final uri = Uri.parse('${ApiConstants.baseUrl}/api/modules').replace(
        queryParameters: {
          'class': classLevel.toString(),
        },
      );

      final response = await HttpClientService.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'X-Package-ID': AppConstants.packageName,
        },
      );

      print('🎓 Modules API response for class $classLevel: ${response.body}');

      final modulesResponse = LearningModulesResponse.fromJson(json.decode(response.body));

      // Debug: Print modules loaded
      if (modulesResponse.data != null) {
        print('📚 Modules loaded for class $classLevel: ${modulesResponse.data!.length}');
        for (var module in modulesResponse.data!) {
          print('📖 Module: ${module.title} - Class: ${module.classLevel} - Premium: ${module.isPremium}');
        }
      }

      return modulesResponse;
    } catch (e) {
      print('Error fetching modules for class $classLevel: $e');
      return LearningModulesResponse(
        success: false,
        message: 'Failed to fetch modules: $e',
      );
    }
  }

  // Get all learning modules (general endpoint)
  static Future<LearningModulesResponse> getAllLearningModules({
    int page = 1,
    int limit = 10,
    String? search,
    String? topic,
    String? difficulty,
    bool? isPremium,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (topic != null && topic.isNotEmpty) {
        queryParams['topic'] = topic;
      }
      if (difficulty != null && difficulty.isNotEmpty) {
        queryParams['difficulty'] = difficulty;
      }
      if (isPremium != null) {
        queryParams['isPremium'] = isPremium.toString();
      }

      final uri = Uri.parse('${ApiConstants.baseUrl}/api/learning-modules').replace(
        queryParameters: queryParams,
      );

      final response = await HttpClientService.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'X-Package-ID': AppConstants.packageName,
        },
      );

      return LearningModulesResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error fetching learning modules: $e');
      return LearningModulesResponse(
        success: false,
        message: 'Failed to fetch learning modules: $e',
      );
    }
  }

  // Get single learning module by ID
  static Future<LearningModule?> getLearningModuleById(String moduleId) async {
    try {
      final response = await HttpClientService.get(
        Uri.parse('${ApiConstants.baseUrl}/api/learning-modules/$moduleId'),
        headers: {
          'Content-Type': 'application/json',
          'X-Package-ID': AppConstants.packageName,
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] && data['data'] != null) {
          return LearningModule.fromJson(data['data']);
        }
      }
      return null;
    } catch (e) {
      print('Error fetching learning module: $e');
      return null;
    }
  }

  // Get modules filtered by multiple criteria
  static Future<LearningModulesResponse> getFilteredModules({
    int? classLevel,
    String? subject,
    String? difficulty,
    bool? isPremium,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (classLevel != null) {
        queryParams['class'] = classLevel.toString();
      }
      if (subject != null && subject.isNotEmpty) {
        queryParams['subject'] = subject;
      }
      if (difficulty != null && difficulty.isNotEmpty) {
        queryParams['difficulty'] = difficulty;
      }
      if (isPremium != null) {
        queryParams['isPremium'] = isPremium.toString();
      }

      final uri = Uri.parse('${ApiConstants.baseUrl}/api/modules').replace(
        queryParameters: queryParams,
      );

      final response = await HttpClientService.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'X-Package-ID': AppConstants.packageName,
        },
      );

      return LearningModulesResponse.fromJson(json.decode(response.body));
    } catch (e) {
      print('Error fetching filtered modules: $e');
      return LearningModulesResponse(
        success: false,
        message: 'Failed to fetch filtered modules: $e',
      );
    }
  }

  // Get available class levels
  static List<int> getAvailableClassLevels() {
    return List.generate(9, (index) => index + 1); // Classes 1-9
  }

  // Get subjects for a specific class
  static Future<List<String>> getSubjectsForClass(int classLevel) async {
    try {
      final modulesResponse = await getModulesByClass(classLevel);
      if (modulesResponse.success && modulesResponse.data != null) {
        final subjects = modulesResponse.data!
            .map((module) => module.subject)
            .where((subject) => subject.isNotEmpty)
            .toSet()
            .toList();
        return subjects;
      }
      return [];
    } catch (e) {
      print('Error fetching subjects for class $classLevel: $e');
      return [];
    }
  }

  // Get difficulty levels
  static List<String> getDifficultyLevels() {
    return ['beginner', 'intermediate', 'advanced'];
  }

  // Helper method to get module color as Color object
  static int getModuleColorValue(String colorString) {
    try {
      // Remove # if present and convert to int
      final cleanColor = colorString.replaceAll('#', '');
      return int.parse('FF$cleanColor', radix: 16);
    } catch (e) {
      // Return default blue color if parsing fails
      return 0xFF007bff;
    }
  }
}
