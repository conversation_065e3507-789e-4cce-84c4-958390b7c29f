import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../../../services/auth_service.dart';

class TextContentData {
  final String id;
  final String packageId;
  final String title;
  final String description;
  final String contentType;
  final String content;
  final String contentFormat;
  final int estimatedReadingTime;
  final String difficulty;
  final bool isActive;
  final bool isPremium;
  final int order;
  final List<String> tags;
  final String contentPreview;
  final int wordCount;
  final bool hasAccess;
  final Map<String, dynamic> metadata;

  TextContentData({
    required this.id,
    required this.packageId,
    required this.title,
    required this.description,
    required this.contentType,
    required this.content,
    required this.contentFormat,
    required this.estimatedReadingTime,
    required this.difficulty,
    required this.isActive,
    required this.isPremium,
    required this.order,
    required this.tags,
    required this.contentPreview,
    required this.wordCount,
    required this.hasAccess,
    required this.metadata,
  });

  factory TextContentData.fromJson(Map<String, dynamic> json) {
    return TextContentData(
      id: json['id'] ?? json['_id'] ?? '',
      packageId: json['packageId'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      contentType: json['contentType'] ?? 'text',
      content: json['content'] ?? '',
      contentFormat: json['contentFormat'] ?? 'plain',
      estimatedReadingTime: json['estimatedReadingTime'] ?? 0,
      difficulty: json['difficulty'] ?? 'beginner',
      isActive: json['isActive'] ?? true,
      isPremium: json['isPremium'] ?? false,
      order: json['order'] ?? 0,
      tags: List<String>.from(json['tags'] ?? []),
      contentPreview: json['contentPreview'] ?? '',
      wordCount: json['wordCount'] ?? 0,
      hasAccess: json['hasAccess'] ?? false,
      metadata: json['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'packageId': packageId,
      'title': title,
      'description': description,
      'contentType': contentType,
      'content': content,
      'contentFormat': contentFormat,
      'estimatedReadingTime': estimatedReadingTime,
      'difficulty': difficulty,
      'isActive': isActive,
      'isPremium': isPremium,
      'order': order,
      'tags': tags,
      'contentPreview': contentPreview,
      'wordCount': wordCount,
      'hasAccess': hasAccess,
      'metadata': metadata,
    };
  }
}

class TextContentService {
  static const String baseUrl = 'https://learner.netaapp.in';
  static const String packageId = 'com.gumbo.english';

  static Future<Map<String, String>> get _headers async {
    final headers = {
      'Content-Type': 'application/json',
      'X-Package-ID': packageId,
    };

    // Add Authorization header if user is logged in
    final token = await AuthService.getAccessToken();
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  /// Get text content by ID
  static Future<TextContentData?> getTextContentById(String contentId) async {
    try {
      final url = Uri.parse('$baseUrl/api/text-content/$contentId');
      final headers = await _headers;

      debugPrint('🔍 TEXT CONTENT API CALL:');
      debugPrint('📍 URL: $url');
      debugPrint('📋 Headers: $headers');
      debugPrint('🆔 Content ID: $contentId');

      final response = await http.get(url, headers: headers);

      debugPrint('📡 API RESPONSE:');
      debugPrint('📊 Status Code: ${response.statusCode}');
      debugPrint('📄 Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        debugPrint('✅ JSON Parsed Successfully');
        debugPrint('🔍 Success: ${jsonData['success']}');
        debugPrint('📦 Data exists: ${jsonData['data'] != null}');

        if (jsonData['success'] && jsonData['data'] != null) {
          final textContent = TextContentData.fromJson(jsonData['data']);
          debugPrint('✅ TextContentData created successfully');
          debugPrint('🔓 Has Access: ${textContent.hasAccess}');
          debugPrint('📝 Title: ${textContent.title}');
          debugPrint('🏷️ Content Type: ${textContent.contentType}');
          return textContent;
        } else {
          debugPrint('❌ API returned success=false or data=null');
        }
      } else {
        debugPrint('❌ HTTP Error: ${response.statusCode}');
        debugPrint('❌ Error Body: ${response.body}');

        // Provide more specific error information
        if (response.statusCode == 404) {
          throw Exception('Text content not found (404). Content ID: $contentId');
        } else if (response.statusCode == 401) {
          throw Exception('Authentication required (401). Please log in again.');
        } else if (response.statusCode == 403) {
          throw Exception('Access denied (403). You may not have permission to view this content.');
        } else if (response.statusCode >= 500) {
          throw Exception('Server error (${response.statusCode}). Please try again later.');
        } else {
          throw Exception('HTTP Error ${response.statusCode}: ${response.body}');
        }
      }
      return null;
    } catch (e) {
      debugPrint('💥 EXCEPTION in getTextContentById: $e');
      debugPrint('📍 Stack trace: ${StackTrace.current}');

      // Re-throw the exception to preserve the original error message
      rethrow;
    }
  }

  /// Record text content view
  static Future<bool> recordTextContentView(String contentId) async {
    try {
      final url = Uri.parse('$baseUrl/api/text-content/$contentId/view');
      final headers = await _headers;
      final response = await http.post(url, headers: headers);
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Error recording text content view: $e');
      return false;
    }
  }

  /// Get reading time estimate
  static int estimateReadingTime(String content, {int wordsPerMinute = 200}) {
    if (content.isEmpty) return 0;
    
    // Remove markdown/html formatting for word count
    final cleanContent = content
        .replaceAll(RegExp(r'[#*_`\[\](){}]'), '')
        .replaceAll(RegExp(r'<[^>]*>'), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
    
    final wordCount = cleanContent.split(' ').length;
    return (wordCount / wordsPerMinute).ceil();
  }

  /// Get content type display name
  static String getContentTypeDisplayName(String contentType) {
    switch (contentType.toLowerCase()) {
      case 'summary':
        return 'Summary';
      case 'reading':
        return 'Reading';
      case 'instructions':
        return 'Instructions';
      case 'notes':
        return 'Notes';
      case 'explanation':
        return 'Explanation';
      case 'guide':
        return 'Guide';
      default:
        return 'Text Content';
    }
  }

  /// Get content type icon
  static IconData getContentTypeIcon(String contentType) {
    switch (contentType.toLowerCase()) {
      case 'summary':
        return Icons.summarize;
      case 'reading':
        return Icons.menu_book;
      case 'instructions':
        return Icons.list_alt;
      case 'notes':
        return Icons.note;
      case 'explanation':
        return Icons.lightbulb_outline;
      case 'guide':
        return Icons.book;
      default:
        return Icons.article;
    }
  }

  /// Get difficulty color
  static Color getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return const Color(0xFF4CAF50); // Green
      case 'intermediate':
        return const Color(0xFFFF9800); // Orange
      case 'advanced':
        return const Color(0xFFF44336); // Red
      default:
        return const Color(0xFF6C5CE7); // Purple
    }
  }

  /// Format reading time
  static String formatReadingTime(int minutes) {
    if (minutes <= 0) return 'Less than 1 min';
    if (minutes == 1) return '1 min read';
    return '$minutes min read';
  }
}
