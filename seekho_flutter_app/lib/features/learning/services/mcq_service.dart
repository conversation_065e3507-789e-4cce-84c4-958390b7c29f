import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../models/mcq_models.dart';

class MCQService {
  static const String baseUrl = 'https://learner.netaapp.in';
  static const String packageId = 'com.gumbo.english';

  static Map<String, String> get _headers => {
        'Content-Type': 'application/json',
        'X-Package-ID': packageId,
      };

  /// Get MCQ data by ID
  static Future<MCQData?> getMCQById(String mcqId) async {
    try {
      final url = Uri.parse('$baseUrl/api/mcqs/$mcqId');
      final response = await http.get(url, headers: _headers);

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['success'] && jsonData['data'] != null) {
          return MCQData.fromJson(jsonData['data']);
        }
      }
      return null;
    } catch (e) {
      throw Exception('Error fetching MCQ: $e');
    }
  }

  /// Submit MCQ answers and get results
  static Future<bool> submitMCQAnswers({
    required String mcqId,
    required List<MCQAnswer> answers,
    required int timeTaken,
  }) async {
    try {
      final url = Uri.parse('$baseUrl/api/mcqs/$mcqId/submit');
      final body = {
        'answers': answers.map((answer) => {
          'questionId': answer.questionId,
          'selectedOptionId': answer.selectedOptionId,
        }).toList(),
        'timeTaken': timeTaken,
      };

      final response = await http.post(
        url,
        headers: _headers,
        body: json.encode(body),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return jsonData['success'] ?? false;
      }
      return false;
    } catch (e) {
      print('Error submitting MCQ answers: $e');
      return false;
    }
  }

  /// Get user's MCQ attempt history
  static Future<List<Map<String, dynamic>>> getMCQAttempts(String mcqId) async {
    try {
      final url = Uri.parse('$baseUrl/api/mcqs/$mcqId/attempts');
      final response = await http.get(url, headers: _headers);

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['success'] && jsonData['data'] != null) {
          return List<Map<String, dynamic>>.from(jsonData['data']);
        }
      }
      return [];
    } catch (e) {
      print('Error fetching MCQ attempts: $e');
      return [];
    }
  }

  /// Calculate MCQ score and results
  static MCQResult calculateResults({
    required String mcqId,
    required MCQData mcqData,
    required List<MCQAnswer> answers,
    required int timeTaken,
  }) {
    int correctAnswers = 0;
    int totalPoints = 0;

    // Calculate correct answers and total points
    for (final question in mcqData.questions) {
      totalPoints += question.points;
      final userAnswer = answers.firstWhere(
        (answer) => answer.questionId == question.id,
        orElse: () => MCQAnswer(
          questionId: question.id,
          selectedOptionId: '',
          isCorrect: false,
          answeredAt: DateTime.now(),
        ),
      );
      
      if (userAnswer.isCorrect) {
        correctAnswers += question.points;
      }
    }

    final percentage = totalPoints > 0 ? (correctAnswers / totalPoints) * 100 : 0.0;
    final passed = percentage >= mcqData.passingScore;

    return MCQResult(
      mcqId: mcqId,
      answers: answers,
      score: correctAnswers,
      totalQuestions: mcqData.questions.length,
      percentage: percentage,
      passed: passed,
      timeTaken: timeTaken,
      completedAt: DateTime.now(),
    );
  }

  /// Validate user's answer for a question
  static bool validateAnswer(MCQQuestion question, String selectedOptionId) {
    // This would typically come from the backend, but for now we'll use a simple approach
    // In a real implementation, the correct answer would be stored securely on the backend
    
    // For demo purposes, we'll assume the first option is correct
    // In production, this logic should be handled by the backend
    return question.options.isNotEmpty && question.options.first.id == selectedOptionId;
  }

  /// Get question difficulty color
  static Color getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return const Color(0xFF4CAF50); // Green
      case 'medium':
        return const Color(0xFFFF9800); // Orange
      case 'hard':
        return const Color(0xFFF44336); // Red
      default:
        return const Color(0xFF6C5CE7); // Purple
    }
  }

  /// Get question difficulty icon
  static IconData getDifficultyIcon(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return Icons.sentiment_satisfied;
      case 'medium':
        return Icons.sentiment_neutral;
      case 'hard':
        return Icons.sentiment_dissatisfied;
      default:
        return Icons.help_outline;
    }
  }

  /// Format time duration
  static String formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}
