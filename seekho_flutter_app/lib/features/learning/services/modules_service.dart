import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/module_models.dart';

class ModulesService {
  static const String baseUrl = 'https://learner.netaapp.in';
  static const String packageId = 'com.gumbo.english';

  static Map<String, String> get _headers => {
        'Content-Type': 'application/json',
        'X-Package-ID': packageId,
      };

  /// Get modules for a specific class (1-9)
  static Future<ModulesResponse> getModulesByClass(int classNumber) async {
    try {
      final url = Uri.parse('$baseUrl/api/modules?class=$classNumber');
      final response = await http.get(url, headers: _headers);

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return ModulesResponse.fromJson(jsonData);
      } else {
        throw Exception('Failed to load modules: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching modules: $e');
    }
  }

  /// Get all learning modules (more detailed with content)
  static Future<ModulesResponse> getAllLearningModules({
    int page = 1,
    int limit = 20,
    String? search,
    String? topic,
    String? difficulty,
    bool? isPremium,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (topic != null && topic.isNotEmpty) {
        queryParams['topic'] = topic;
      }
      if (difficulty != null && difficulty.isNotEmpty) {
        queryParams['difficulty'] = difficulty;
      }
      if (isPremium != null) {
        queryParams['isPremium'] = isPremium.toString();
      }

      final uri = Uri.parse('$baseUrl/api/learning-modules').replace(
        queryParameters: queryParams,
      );

      final response = await http.get(uri, headers: _headers);

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return ModulesResponse.fromJson(jsonData);
      } else {
        throw Exception('Failed to load learning modules: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching learning modules: $e');
    }
  }

  /// Get modules filtered by class number from learning modules
  static Future<List<LearningModule>> getLearningModulesByClass(int classNumber) async {
    try {
      final response = await getAllLearningModules();
      return response.data
          .where((module) => module.classNumber == classNumber)
          .toList();
    } catch (e) {
      throw Exception('Error fetching modules for class $classNumber: $e');
    }
  }

  /// Get modules grouped by class (1-9)
  static Future<Map<int, List<LearningModule>>> getModulesGroupedByClass() async {
    try {
      final response = await getAllLearningModules(limit: 100); // Get more modules
      final Map<int, List<LearningModule>> groupedModules = {};

      for (final module in response.data) {
        if (module.classNumber != null) {
          final classNum = module.classNumber!;
          if (!groupedModules.containsKey(classNum)) {
            groupedModules[classNum] = [];
          }
          groupedModules[classNum]!.add(module);
        }
      }

      // Sort modules within each class by order
      for (final classModules in groupedModules.values) {
        classModules.sort((a, b) => a.order.compareTo(b.order));
      }

      return groupedModules;
    } catch (e) {
      throw Exception('Error fetching grouped modules: $e');
    }
  }

  /// Get user's current class from stored preferences or API
  static Future<int> getUserClass() async {
    // TODO: Implement getting user's class from onboarding or user profile
    // For now, return a default class
    return 1; // This should be replaced with actual user class from onboarding
  }

  /// Get modules for user's current class
  static Future<List<LearningModule>> getModulesForCurrentUser() async {
    try {
      final userClass = await getUserClass();
      return await getLearningModulesByClass(userClass);
    } catch (e) {
      throw Exception('Error fetching modules for current user: $e');
    }
  }

  /// Get a single learning module with populated content
  static Future<LearningModule?> getModuleWithContent(String moduleId) async {
    try {
      final url = Uri.parse('$baseUrl/api/learning-modules/$moduleId');
      final response = await http.get(url, headers: _headers);

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['success'] && jsonData['data'] != null) {
          return LearningModule.fromJson(jsonData['data']);
        }
      }
      return null;
    } catch (e) {
      throw Exception('Error fetching module with content: $e');
    }
  }

  /// Helper method to get content type display name
  static String getContentTypeDisplayName(String contentType) {
    switch (contentType.toLowerCase()) {
      case 'video':
        return 'Video Lesson';
      case 'questionnaire':
        return 'Questionnaire';
      case 'mcq':
        return 'Multiple Choice Quiz';
      case 'summary':
        return 'Summary';
      case 'reading':
        return 'Reading Material';
      case 'instructions':
        return 'Instructions';
      case 'notes':
        return 'Notes';
      default:
        return 'Lesson';
    }
  }

  /// Helper method to get content type icon
  static String getContentTypeIcon(String contentType) {
    switch (contentType.toLowerCase()) {
      case 'video':
        return '🎥';
      case 'questionnaire':
      case 'mcq':
        return '📝';
      case 'summary':
      case 'reading':
      case 'notes':
        return '📖';
      case 'instructions':
        return '📋';
      default:
        return '📚';
    }
  }
}
