import 'package:bolo_app/utils/constants.dart';
import 'package:google_sign_in/google_sign_in.dart';

import 'auth_service.dart';

class GoogleSignInService {
  static GoogleSignIn? _googleSignIn;
  
  // Initialize Google Sign-In with dynamic client ID
  static Future<void> initialize() async {
    try {
      // Get Android config from backend
      final config = await AuthService.getAndroidConfig();
      final clientId = config?.androidClientId ?? AppConstants.androidClientId;
      
      _googleSignIn = GoogleSignIn(
        // clientId: clientId,
        scopes: [
          'email',
          'profile',
        ],
      );
    } catch (e) {
      print('Error initializing Google Sign-In: $e');
      // Fallback to hardcoded client ID
      _googleSignIn = GoogleSignIn(
        // clientId: AppConstants.androidClientId,
        scopes: [
          'email',
          'profile',
        ],
      );
    }
  }

  // Sign in with Google
  static Future<GoogleSignInAccount?> signIn() async {
    try {
      if (_googleSignIn == null) {
        await initialize();
      }

      print('🔍 DEBUG: Starting Google Sign-In...');
      print('🔍 DEBUG: Client ID being used: ${_googleSignIn?.clientId}');

      final GoogleSignInAccount? account = await _googleSignIn!.signIn();

      if (account == null) {
        print('🔍 DEBUG: Google Sign-In returned null (user cancelled or error)');
      } else {
        print('🔍 DEBUG: Google Sign-In successful for: ${account.email}');
      }

      return account;
    } catch (e) {
      print('🔍 ERROR: Google Sign-In failed with error: $e');
      print('🔍 ERROR: Error type: ${e.runtimeType}');
      return null;
    }
  }

  // Get ID token from Google account
  static Future<String?> getIdToken(GoogleSignInAccount account) async {
    try {
      final GoogleSignInAuthentication auth = await account.authentication;
      return auth.idToken;
    } catch (e) {
      print('Error getting ID token: $e');
      return null;
    }
  }

  // Sign out from Google
  static Future<void> signOut() async {
    try {
      if (_googleSignIn == null) {
        await initialize();
      }
      await _googleSignIn!.signOut();
    } catch (e) {
      print('Error signing out from Google: $e');
    }
  }

  // Disconnect from Google
  static Future<void> disconnect() async {
    try {
      if (_googleSignIn == null) {
        await initialize();
      }
      await _googleSignIn!.disconnect();
    } catch (e) {
      print('Error disconnecting from Google: $e');
    }
  }

  // Check if user is signed in to Google
  static Future<bool> isSignedIn() async {
    try {
      if (_googleSignIn == null) {
        await initialize();
      }
      return await _googleSignIn!.isSignedIn();
    } catch (e) {
      print('Error checking Google sign-in status: $e');
      return false;
    }
  }

  // Get current Google account
  static Future<GoogleSignInAccount?> getCurrentAccount() async {
    try {
      if (_googleSignIn == null) {
        await initialize();
      }
      return _googleSignIn!.currentUser;
    } catch (e) {
      print('Error getting current Google account: $e');
      return null;
    }
  }
}
