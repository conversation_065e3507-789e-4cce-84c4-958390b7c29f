import 'user.dart';

class AuthResponse {
  final bool success;
  final String message;
  final AuthData? data;

  AuthResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? AuthData.fromJson(json['data']) : null,
    );
  }
}

class AuthData {
  final String token;
  final User user;

  AuthData({
    required this.token,
    required this.user,
  });

  factory AuthData.fromJson(Map<String, dynamic> json) {
    return AuthData(
      token: json['token'] ?? '',
      user: User.from<PERSON><PERSON>(json['user'] ?? {}),
    );
  }
}

class AndroidConfigResponse {
  final bool success;
  final AndroidConfigData? data;

  AndroidConfigResponse({
    required this.success,
    this.data,
  });

  factory AndroidConfigResponse.from<PERSON>son(Map<String, dynamic> json) {
    return AndroidConfigResponse(
      success: json['success'] ?? false,
      data: json['data'] != null ? AndroidConfigData.fromJson(json['data']) : null,
    );
  }
}

class AndroidConfigData {
  final String androidClientId;
  final String packageName;
  final String deepLink;

  AndroidConfigData({
    required this.androidClientId,
    required this.packageName,
    required this.deepLink,
  });

  factory AndroidConfigData.fromJson(Map<String, dynamic> json) {
    return AndroidConfigData(
      androidClientId: json['androidClientId'] ?? '',
      packageName: json['packageName'] ?? '',
      deepLink: json['deepLink'] ?? '',
    );
  }
}
