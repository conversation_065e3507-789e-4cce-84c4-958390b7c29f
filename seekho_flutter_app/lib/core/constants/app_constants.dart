class ApiConstants {
  static const String baseUrl = 'https://learner.netaapp.in';

  // Authentication endpoints
  static const String androidGoogleAuth = '/api/auth/android/google';
  static const String androidConfig = '/api/auth/android/config';
  static const String androidRefresh = '/api/auth/android/refresh';
  static const String androidLogout = '/api/auth/android/logout';
  static const String emailLogin = '/api/auth/login'; // Email/password login endpoint

  // Content endpoints
  static const String categories = '/api/categories';
  static const String topics = '/api/topics';
  static const String videos = '/api/videos';
  static const String questionnaires = '/api/questionnaires';

  // Enhanced content endpoints
  static const String videosSearch = '/api/videos/search';
  static const String videosPopular = '/api/videos/popular';
  static const String videosNew = '/api/videos/new';
  static const String videosStream = '/api/videos';
  static const String videosProgress = '/api/videos';
  static const String videosView = '/api/videos';
  static const String videosRelated = '/api/videos';

  // User endpoints
  static const String userProfile = '/api/users/profile';
  static const String userStats = '/api/users/stats';
  static const String userWatchHistory = '/api/users/watch-history';
  static const String userFavorites = '/api/users/favorites';
  static const String userBookmarks = '/api/users/bookmarks';

  // Notification endpoints
  static const String notifications = '/api/notifications';
  static const String notificationsMarkRead = '/api/notifications/mark-read';
  static const String notificationsUnreadCount = '/api/notifications/unread-count';

  // Subscription endpoints
  static const String subscriptionPlans = '/api/subscriptions/plans';
  static const String subscriptionList = '/api/subscriptions/list'; // New endpoint for subscriptionList format
  static const String subscriptionStatus = '/api/subscriptions/status';
  static const String trialEligibility = '/api/subscriptions/trial-eligibility';
  static const String createOrder = '/api/subscriptions/create-order';
  static const String verifyPayment = '/api/subscriptions/verify-payment';
  static const String createTrialWithMandate = '/api/subscriptions/create-trial-with-mandate';
}

class StorageKeys {
  static const String accessToken = 'access_token';
  static const String refreshToken = 'refresh_token';
  static const String userInfo = 'user_info';
  static const String isLoggedIn = 'is_logged_in';
}

class AppConstants {
  static const String appName = 'English Guru  App';
  static const String packageName = 'com.gumbo.english';

  // These will be fetched from the API, but keeping as fallback
  static const String androidClientId = '601890245278-tgja9b5g58oih85addm5ntptiuoobu7g.apps.googleusercontent.com';

  // Timer constants
  static const int countdownTimerMinutes = 59;

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double defaultBorderRadius = 12.0;
  static const double cardElevation = 4.0;

  // Animation durations
  static const int defaultAnimationDuration = 300;
  static const int longAnimationDuration = 800;

  // Validation constants
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  static const int minAge = 5;
  static const int maxAge = 100;
  static const int minClassLevel = 1;
  static const int maxClassLevel = 12;
}
