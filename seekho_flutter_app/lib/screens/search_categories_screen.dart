import 'package:flutter/material.dart';
import '../models/category.dart';
import '../models/video.dart';
import '../services/content_service.dart';
import 'unified_subscription_screen.dart';
import '../widgets/premium_lock_icon.dart';
import 'category_detail_screen.dart';

class SearchCategoriesScreen extends StatefulWidget {
  final List<Category> categories;

  const SearchCategoriesScreen({
    super.key,
    required this.categories,
  });

  @override
  State<SearchCategoriesScreen> createState() => _SearchCategoriesScreenState();
}

class _SearchCategoriesScreenState extends State<SearchCategoriesScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Video> _popularVideos = [];
  List<Video> _searchResults = [];
  bool _isLoadingPopular = true;
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _loadPopularVideos();
  }

  Future<void> _loadPopularVideos() async {
    setState(() => _isLoadingPopular = true);

    try {
      final response = await ContentService.getPopularVideos(limit: 6);
      if (response.success && response.data != null) {
        setState(() {
          _popularVideos = response.data!;
          _isLoadingPopular = false;
        });
      } else {
        setState(() => _isLoadingPopular = false);
      }
    } catch (e) {
      setState(() => _isLoadingPopular = false);
    }
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    setState(() => _isSearching = true);

    try {
      final response = await ContentService.searchVideos(query);
      if (response.success && response.data != null) {
        setState(() {
          _searchResults = response.data!;
          _isSearching = false;
        });
      } else {
        setState(() {
          _searchResults = [];
          _isSearching = false;
        });
      }
    } catch (e) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      body: SafeArea(
        child: Column(
          children: [
            _buildSearchHeader(),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPopularShowsSection(),
                    const SizedBox(height: 24),
                    _buildAllCategoriesSection(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back, color: Colors.white),
          ),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFF3A3A3A),
                borderRadius: BorderRadius.circular(25),
              ),
              child: TextField(
                controller: _searchController,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  hintText: 'Search Here',
                  hintStyle: TextStyle(color: Colors.grey),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 15,
                  ),
                  prefixIcon: Icon(Icons.search, color: Colors.grey),
                ),
                onSubmitted: (query) {
                  _performSearch(query);
                },
                onChanged: (query) {
                  if (query.isEmpty) {
                    setState(() {
                      _searchResults = [];
                      _isSearching = false;
                    });
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPopularShowsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF3A3A3A),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.play_circle_outline,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Popular Shows',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to view all popular shows
              },
              child: const Text(
                'View all',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _popularVideos.length,
            itemBuilder: (context, index) {
              final video = _popularVideos[index];
              return Container(
                width: 200,
                margin: const EdgeInsets.only(right: 12),
                child: _buildPopularVideoCard(video),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPopularVideoCard(Video video) {
    return GestureDetector(
      onTap: () {
        if (video.isPremium) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const UnifiedSubscriptionScreen(),
            ),
          );
        } else {
          // TODO: Navigate to video player
          print('Navigate to video: ${video.title}');
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              Colors.orange.withValues(alpha: 0.8),
              Colors.deepOrange.withValues(alpha: 0.6),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Stack(
          children: [
            Positioned.fill(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: video.thumbnail != null && video.thumbnail!.isNotEmpty
                    ? Image.network(
                        video.thumbnail!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey[800],
                            child: const Icon(
                              Icons.video_library,
                              color: Colors.white,
                              size: 40,
                            ),
                          );
                        },
                      )
                    : Container(
                        color: Colors.grey[800],
                        child: const Icon(
                          Icons.video_library,
                          color: Colors.white,
                          size: 40,
                        ),
                      ),
              ),
            ),

            // Premium lock overlay
            Positioned(
              top: 8,
              right: 8,
              child: PremiumLockIcon(
                isPremium: video.isPremium,
                size: 16,
                padding: const EdgeInsets.all(6),
                borderRadius: 20,
              ),
            ),

            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),

            Positioned(
              bottom: 12,
              left: 12,
              right: 12,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    video.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (video.isPremium)
                    Container(
                      margin: const EdgeInsets.only(top: 4),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Text(
                        'Premium',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildAllCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(
              Icons.trending_up,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            const Text(
              'All categories',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 2.2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: widget.categories.length,
          itemBuilder: (context, index) {
            final category = widget.categories[index];
            return _buildCategoryTile(category);
          },
        ),
      ],
    );
  }

  Widget _buildCategoryTile(Category category) {
    final colors = _getCategoryColors(category.name);

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CategoryDetailScreen(category: category),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: colors,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Stack(
          children: [
            Positioned(
              bottom: 12,
              left: 12,
              child: Text(
                category.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Positioned(
              top: 12,
              right: 12,
              child: Icon(
                _getCategoryIcon(category.name),
                color: Colors.white.withValues(alpha: 0.7),
                size: 24,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Color> _getCategoryColors(String categoryName) {
    switch (categoryName.toLowerCase()) {
      case 'sarkari kaam':
        return [Colors.orange, Colors.deepOrange];
      case 'parttime':
        return [Colors.green, Colors.lightGreen];
      case 'gaming':
        return [Colors.red, Colors.pink];
      case 'instagram':
        return [Colors.purple, Colors.pinkAccent];
      case 'youtube':
        return [Colors.red, Colors.redAccent];
      case 'english speaking':
        return [Colors.teal, Colors.cyan];
      case 'astrology':
        return [Colors.purple, Colors.deepPurple];
      case 'finance':
        return [Colors.blue, Colors.lightBlue];
      default:
        return [Colors.grey[700]!, Colors.grey[600]!];
    }
  }

  IconData _getCategoryIcon(String categoryName) {
    switch (categoryName.toLowerCase()) {
      case 'sarkari kaam':
        return Icons.work;
      case 'parttime':
        return Icons.access_time;
      case 'gaming':
        return Icons.games;
      case 'instagram':
        return Icons.camera_alt;
      case 'youtube':
        return Icons.play_arrow;
      case 'english speaking':
        return Icons.record_voice_over;
      case 'astrology':
        return Icons.star;
      case 'finance':
        return Icons.attach_money;
      default:
        return Icons.category;
    }
  }
}
