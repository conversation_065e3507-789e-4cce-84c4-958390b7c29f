import 'package:bolo_app/screens/enhanced_profile_screen.dart';
import 'package:flutter/material.dart';
import '../models/category.dart';
import '../models/video.dart';
import '../models/user.dart';
import '../services/content_service.dart';
import '../services/auth_service.dart';
import '../services/new_subscription_service.dart';
import '../services/user_service.dart';
import '../services/countdown_timer_service.dart';
import 'category_detail_screen.dart';
import 'all_categories_screen.dart';
import 'video_player_screen.dart';
import '../widgets/my_library_view.dart';
import 'new_content_screen.dart';
import 'profile_screen.dart';
import 'unified_subscription_screen.dart';
import '../widgets/premium_lock_icon.dart';
import 'new_home_screen.dart';

class MainContentScreen extends StatefulWidget {
  const MainContentScreen({super.key});

  @override
  State<MainContentScreen> createState() => _MainContentScreenState();
}

class _MainContentScreenState extends State<MainContentScreen> {
  List<Category> _categories = [];
  List<Video> _allVideos = [];
  bool _isLoading = true;
  int _selectedIndex = 0;

  User? _currentUser;
  String _currentTimerValue = '59:00';

  @override
  void initState() {
    super.initState();
    _loadContent();
    _loadUserData();
    _initializeTimer();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _initializeTimer() async {
    // Check if user has premium access first
    final hasPremiumAccess = await NewSubscriptionService.hasAccessToPremiumContent();

    if (hasPremiumAccess) {
      // Premium users don't need timer
      setState(() {
        _currentTimerValue = 'PREMIUM';
      });
      return;
    }

    await CountdownTimerService.initialize();

    // Listen to timer updates
    CountdownTimerService.timerStream.listen((timerValue) {
      if (mounted) {
        setState(() {
          _currentTimerValue = timerValue;
        });
      }
    });

    // Set initial value
    setState(() {
      _currentTimerValue = CountdownTimerService.getCurrentTime();
    });
  }

  Future<void> _loadUserData() async {
    try {
      final user = await AuthService.getStoredUser();
      setState(() {
        _currentUser = user;
      });
    } catch (e) {
      // Handle error gracefully
    }
  }

  Future<void> _loadContent() async {
    setState(() => _isLoading = true);

    try {
      // Load categories and videos in parallel
      final results = await Future.wait([
        ContentService.getCategories(),
        ContentService.getAllVideos(),
      ]);

      final categoriesResponse = results[0] as CategoriesResponse;
      final videosResponse = results[1] as VideosResponse;

      setState(() {
        _categories = categoriesResponse.data ?? [];
        _allVideos = videosResponse.data ?? [];
        _isLoading = false;
      });
    } catch (e) {
      // Handle error gracefully
      setState(() => _isLoading = false);
    }
  }



  void _onBottomNavTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });

    // Handle navigation based on selected index
    switch (index) {
      case 0:
        // Home - stay on current screen, just change the view
        break;
      case 1:
        // New - stay on current screen, just change the view
        break;
      case 2:
        // My Library - stay on current screen, just change the view
        break;
    }
  }

  Widget _buildHomeContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top bar with PLUS badge and profile
          _buildTopBar(),

          const SizedBox(height: 24),

          // Search Bar
          // _buildSearchBar(),

          const SizedBox(height: 24),

          // Categories Grid
          _buildCategoriesGrid(),

          const SizedBox(height: 32),

          // Top Videos Section
          _buildTopVideosSection(),

          const SizedBox(height: 32),

          // Category Sections - dynamically build based on available categories
          ..._buildCategorySections(),

          const SizedBox(height: 100), // Extra space for bottom nav
        ],
      ),
    );
  }

  Widget _buildNewReleasesContent() {
    return const NewContentScreen();
  }

  Widget _buildMyLibraryContent() {
    return MyLibraryView(
      allVideos: _allVideos,
    );
  }

  Widget _getSelectedContent() {
    switch (_selectedIndex) {
      case 0:
        return const NewHomeScreen(); // Use new home screen
      case 1:
        return const EnhancedProfileScreen(); // Open profile screen
      default:
        return const NewHomeScreen(); // Use new home screen
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E), // Match new home screen theme
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : RefreshIndicator(
                onRefresh: _loadContent,
                child: _getSelectedContent(),
              ),
      ),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Premium banner (tappable)
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                // Navigate to subscription screen
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const UnifiedSubscriptionScreen(),
                  ),
                );
              },
              borderRadius: BorderRadius.circular(25),
              child: Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFFFD700).withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
            child: Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.star,
                    color: Color(0xFFFFD700),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'ENGLISH GURU PRO',
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Text(
                    _currentTimerValue,
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                const Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.black,
                  size: 16,
                ),
              ],
            ),
            ),
            ),
          ),

          // Bottom navigation
          Container(
            decoration: const BoxDecoration(
              color: Color(0xFF1A1A2E), // Match new theme
              border: Border(
                top: BorderSide(color: Color(0xFF2A2A3E), width: 0.5),
              ),
            ),
            child: BottomNavigationBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              currentIndex: _selectedIndex,
              onTap: _onBottomNavTapped,
              type: BottomNavigationBarType.fixed,
              selectedItemColor: const Color(0xFF6C63FF), // Purple theme
              unselectedItemColor: Colors.grey,
              items: const [
                BottomNavigationBarItem(
                  icon: Icon(Icons.school),
                  label: 'Learn',
                ),
                // BottomNavigationBarItem(
                //   icon: Icon(Icons.chat_bubble_outline),
                //   label: 'Practice',
                // ),
                // BottomNavigationBarItem(
                //   icon: Icon(Icons.quiz),
                //   label: 'IELTS',
                // ),
                // BottomNavigationBarItem(
                //   icon: Icon(Icons.call),
                //   label: 'Call',
                // ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.person_outline),
                  label: 'Profile',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar() {
    final greeting = _getTimeBasedGreeting();
    final userName = _currentUser?.name != null
        ? _currentUser!.name.split(' ').first
        : 'User';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // PLUS badge - only show for non-premium users
            FutureBuilder<bool>(
              future: NewSubscriptionService.hasAccessToPremiumContent(),
              builder: (context, snapshot) {
                final hasPremiumAccess = snapshot.data ?? false;

                if (hasPremiumAccess) {
                  // Premium users see a premium badge instead
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFD700),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: const Color(0xFFFFD700), width: 2),
                    ),
                    child: const Text(
                      '👑 PREMIUM',
                      style: TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  );
                }

                // Free users see the upgrade CTA
                return GestureDetector(
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(builder: (context) => const UnifiedSubscriptionScreen()),
                    );
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFD700),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: const Color(0xFFFFD700), width: 2),
                    ),
                    child: const Text(
                      '⚡ PLUS',
                      style: TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                );
              },
            ),
            // Enhanced Profile avatar
            GestureDetector(
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(builder: (context) => const ProfileScreen()),
                );
              },
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: const LinearGradient(
                    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: CircleAvatar(
                  backgroundColor: Colors.transparent,
                  radius: 20,
                  child: Text(
                    userName[0].toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Personalized welcome message
        Text(
          '$greeting, $userName!',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        const Text(
          'Ready to learn something new today?',
          style: TextStyle(
            color: Colors.grey,
            fontSize: 16,
          ),
        ),
      ],
    );
  }

  String _getTimeBasedGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good Morning';
    } else if (hour < 17) {
      return 'Good Afternoon';
    } else {
      return 'Good Evening';
    }
  }



  Widget _buildCategoriesGrid() {
    if (_categories.isEmpty) {
      return const Center(
        child: Text('No categories available', style: TextStyle(color: Colors.white)),
      );
    }

    // Take first 8 categories or add "View All" if more than 7
    final displayCategories = _categories.take(7).toList();
    if (_categories.length > 7) {
      displayCategories.add(Category(
        id: 'view_all',
        name: 'View All',
        description: 'View all categories',
        order: 999,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ));
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 0.9,
      ),
      itemCount: displayCategories.length,
      itemBuilder: (context, index) {
        final category = displayCategories[index];
        return _buildCategoryItem(category);
      },
    );
  }

  Widget _buildCategoryItem(Category category) {
    final categoryConfig = _getCategoryConfig(category.name);

    return GestureDetector(
      onTap: () {
        if (category.id == 'view_all') {
          // Navigate to All Categories screen
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AllCategoriesScreen(categories: _categories),
            ),
          );
        } else {
          // Navigate to specific category detail screen
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CategoryDetailScreen(category: category),
            ),
          );
        }
      },
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: categoryConfig['color'],
              shape: BoxShape.circle,
            ),
            child: Center(
              child: category.name == 'English'
                  ? Text(
                      categoryConfig['icon'],
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : Text(
                      categoryConfig['icon'],
                      style: const TextStyle(
                        fontSize: 24,
                      ),
                    ),
            ),
          ),
          const SizedBox(height: 6),
          Flexible(
            child: Text(
              category.name,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getCategoryConfig(String categoryName) {
    final configs = {
      'Sarkari Kaam': {'icon': '🏛️', 'color': const Color(0xFFFF6B35)},
      'PartTime': {'icon': '💰', 'color': const Color(0xFF4CAF50)},
      'Gaming': {'icon': '🎮', 'color': const Color(0xFFE53E3E)},
      'Instagram': {'icon': '📷', 'color': const Color(0xFFE91E63)},
      'Youtube': {'icon': '▶️', 'color': const Color(0xFFFF0000)},
      'English': {'icon': 'Abc', 'color': const Color(0xFFFFD700)},
      'Astrology': {'icon': '🔮', 'color': const Color(0xFF9C27B0)},
      'View All': {'icon': '⊞', 'color': const Color(0xFF2196F3)},
    };

    return configs[categoryName] ?? {
      'icon': '📁',
      'color': const Color(0xFF607D8B),
    };
  }

  Widget _buildTopVideosSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(
              Icons.trending_up,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            const Text(
              'Top Videos',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildTopVideosGrid(),
      ],
    );
  }

  Widget _buildCategorySection(String title, String categoryId) {
    final categoryVideos = _getVideosByCategory(categoryId);

    if (categoryVideos.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                _getCategoryIcon(title),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            TextButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => AllCategoriesScreen(categories: _categories),
                  ),
                );
              },
              child: const Text(
                'View all',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 180,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: categoryVideos.length,
            itemBuilder: (context, index) {
              return Container(
                width: 140,
                margin: const EdgeInsets.only(right: 12),
                child: _buildCategoryVideoCard(categoryVideos[index], index),
              );
            },
          ),
        ),
      ],
    );
  }

  List<Video> _getVideosByCategory(String categoryId) {
    return _allVideos.where((video) => video.categoryId == categoryId).take(5).toList();
  }

  List<Widget> _buildCategorySections() {
    final widgets = <Widget>[];

    // Build sections for categories that have videos
    for (final category in _categories.take(3)) { // Show first 3 categories with videos
      final categoryVideos = _getVideosByCategory(category.id);
      if (categoryVideos.isNotEmpty) {
        widgets.add(const SizedBox(height: 32));
        widgets.add(_buildCategorySection(category.name, category.id));
      }
    }

    return widgets;
  }

  Widget _buildTopVideosGrid() {
    if (_allVideos.isEmpty) {
      return const Center(
        child: Text('No videos available', style: TextStyle(color: Colors.white)),
      );
    }

    // Take top 6 videos (most viewed or recent)
    final topVideos = _allVideos.take(6).toList();

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.75,
      ),
      itemCount: topVideos.length,
      itemBuilder: (context, index) {
        return _buildTopVideoCard(topVideos[index], index);
      },
    );
  }



  Widget _buildTopVideoCard(Video video, int index) {
    // Define gradient colors for variety (fallback when no thumbnail)
    final gradients = [
      [const Color(0xFFE53E3E), const Color(0xFFFF6B35)],
      [const Color(0xFFE91E63), const Color(0xFF9C27B0)],
      [const Color(0xFF2196F3), const Color(0xFF03DAC6)],
      [const Color(0xFF4CAF50), const Color(0xFF8BC34A)],
      [const Color(0xFFFF5722), const Color(0xFFFF9800)],
      [const Color(0xFF00BCD4), const Color(0xFF009688)],
    ];

    final gradient = gradients[index % gradients.length];
    final categoryName = _getCategoryNameById(video.categoryId);

    return GestureDetector(
      onTap: () async {
        if (video.isPremium) {
          // Check if user has access to premium content (includes test user check)
          final hasAccess = await NewSubscriptionService.hasAccessToPremiumContent();

          if (!hasAccess) {
            if (mounted) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const UnifiedSubscriptionScreen(),
                ),
              );
            }
            return;
          }
        }

        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => VideoPlayerScreen(
                video: video,
                playlist: _allVideos.where((v) => v.categoryId == video.categoryId).toList(),
                initialIndex: _allVideos.where((v) => v.categoryId == video.categoryId).toList().indexOf(video),
              ),
            ),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.grey.shade800,
        ),
        child: Stack(
          children: [
            // Thumbnail or gradient background
            ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: video.thumbnail != null && video.thumbnail!.isNotEmpty
                  ? Image.network(
                      video.thumbnail!,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildGradientBackground(gradient);
                      },
                    )
                  : _buildGradientBackground(gradient),
            ),

            // Dark overlay for better text readability
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),

            // Content overlay
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Category badge and premium lock
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.6),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          categoryName.toUpperCase(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      if (video.isPremium)
                        Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.6),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.lock,
                            color: Color(0xFFFFD700),
                            size: 12,
                          ),
                        ),
                    ],
                  ),
                  const Spacer(),

                  // Play button
                  const Center(
                    child: Icon(
                      Icons.play_circle_filled,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
                  const Spacer(),

                  // Title
                  Text(
                    video.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          offset: Offset(0, 1),
                          blurRadius: 3,
                          color: Colors.black,
                        ),
                      ],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  // Views and duration
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Text(
                        '${video.views} views',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 10,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        video.formattedDuration,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGradientBackground(List<Color> gradient) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: gradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
    );
  }

  String _getCategoryNameById(String categoryId) {
    final category = _categories.firstWhere(
      (cat) => cat.id == categoryId,
      orElse: () => Category(
        id: '',
        name: 'General',
        description: '',
        order: 0,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );
    return category.name;
  }



  Widget _getCategoryIcon(String title) {
    switch (title) {
      case 'Sarkari Kaam':
        return const Text('🏛️', style: TextStyle(fontSize: 20));
      case 'Part Time Income':
        return const Text('💰', style: TextStyle(fontSize: 20));
      case 'Gaming':
        return const Text('🎮', style: TextStyle(fontSize: 20));
      default:
        return const Icon(Icons.category, color: Colors.white, size: 20);
    }
  }

  Widget _buildCategoryVideoCard(Video video, int index) {
    // Define gradient colors for variety
    final gradients = [
      [const Color(0xFFE53E3E), const Color(0xFFFF6B35)],
      [const Color(0xFF2196F3), const Color(0xFF03DAC6)],
      [const Color(0xFF4CAF50), const Color(0xFF8BC34A)],
      [const Color(0xFFFF9800), const Color(0xFFFFD700)],
      [const Color(0xFFE91E63), const Color(0xFF9C27B0)],
    ];

    final gradient = gradients[index % gradients.length];
    final categoryName = _getCategoryNameById(video.categoryId);

    return GestureDetector(
      onTap: () async {
        if (video.isPremium) {
          // Check if user has access to premium content (includes test user check)
          final hasAccess = await NewSubscriptionService.hasAccessToPremiumContent();

          if (!hasAccess) {
            if (mounted) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const UnifiedSubscriptionScreen(),
                ),
              );
            }
            return;
          }
        }

        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => VideoPlayerScreen(
                video: video,
                playlist: _allVideos.where((v) => v.categoryId == video.categoryId).toList(),
                initialIndex: _allVideos.where((v) => v.categoryId == video.categoryId).toList().indexOf(video),
              ),
            ),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.grey.shade800,
        ),
        child: Stack(
          children: [
            // Thumbnail or gradient background
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: video.thumbnail != null && video.thumbnail!.isNotEmpty
                  ? Image.network(
                      video.thumbnail!,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildGradientBackground(gradient);
                      },
                    )
                  : _buildGradientBackground(gradient),
            ),

            // Dark overlay for better text readability
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),

            // Premium lock overlay
            Positioned(
              top: 8,
              right: 8,
              child: PremiumLockIcon(
                isPremium: video.isPremium,
                size: 12,
                padding: const EdgeInsets.all(4),
                borderRadius: 12,
              ),
            ),

            // Play button center
            const Center(
              child: Icon(
                Icons.play_circle_filled,
                color: Colors.white,
                size: 32,
              ),
            ),

            // Duration badge
            Positioned(
              bottom: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  video.formattedDuration,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),

            // Content at bottom
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Category badge
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.6),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        categoryName.toUpperCase(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: 4),
                    // Title
                    Text(
                      video.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            offset: Offset(0, 1),
                            blurRadius: 3,
                            color: Colors.black,
                          ),
                        ],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class NewReleasesView extends StatefulWidget {
  final List<Category> categories;
  final List<Video> allVideos;

  const NewReleasesView({
    super.key,
    required this.categories,
    required this.allVideos,
  });

  @override
  State<NewReleasesView> createState() => _NewReleasesViewState();
}

class _NewReleasesViewState extends State<NewReleasesView> {
  String? _selectedCategoryId;
  List<Video> _filteredVideos = [];

  @override
  void initState() {
    super.initState();
    _filteredVideos = widget.allVideos;
  }

  @override
  void didUpdateWidget(NewReleasesView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.allVideos != widget.allVideos) {
      _applyFilter();
    }
  }

  void _applyFilter() {
    setState(() {
      if (_selectedCategoryId == null) {
        _filteredVideos = widget.allVideos;
      } else {
        _filteredVideos = widget.allVideos
            .where((video) => video.categoryId == _selectedCategoryId)
            .toList();
      }
    });
  }

  void _showCategoryFilter() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Color(0xFF2A2A2A),
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 16),
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Categories',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            Flexible(
              child: ListView(
                shrinkWrap: true,
                children: [
                  _buildCategoryFilterItem(null, 'All Videos', Icons.video_library),
                  ...widget.categories.map((category) =>
                    _buildCategoryFilterItem(category.id, category.name, _getCategoryIcon(category.name))
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryFilterItem(String? categoryId, String name, IconData icon) {
    final isSelected = _selectedCategoryId == categoryId;

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? Colors.white : Colors.grey,
      ),
      title: Text(
        name,
        style: TextStyle(
          color: isSelected ? Colors.white : Colors.grey,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      trailing: isSelected
          ? const Icon(Icons.check, color: Colors.white)
          : null,
      onTap: () {
        setState(() {
          _selectedCategoryId = categoryId;
        });
        _applyFilter();
        Navigator.pop(context);
      },
    );
  }

  IconData _getCategoryIcon(String categoryName) {
    switch (categoryName.toLowerCase()) {
      case 'sarkari kaam':
        return Icons.work;
      case 'part time income':
        return Icons.monetization_on;
      case 'gaming':
        return Icons.sports_esports;
      case 'instagram':
        return Icons.camera_alt;
      case 'english speaking':
        return Icons.record_voice_over;
      case 'health':
        return Icons.health_and_safety;
      default:
        return Icons.category;
    }
  }



  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with filter
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'New Releases',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                onPressed: _showCategoryFilter,
                icon: const Icon(
                  Icons.tune,
                  color: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Filter chip
          if (_selectedCategoryId != null)
            Container(
              margin: const EdgeInsets.only(bottom: 16),
              child: Chip(
                label: Text(
                  widget.categories
                      .firstWhere((cat) => cat.id == _selectedCategoryId)
                      .name,
                  style: const TextStyle(color: Colors.white),
                ),
                backgroundColor: const Color(0xFF3A3A3A),
                deleteIcon: const Icon(Icons.close, color: Colors.white, size: 18),
                onDeleted: () {
                  setState(() {
                    _selectedCategoryId = null;
                  });
                  _applyFilter();
                },
              ),
            ),

          const SizedBox(height: 16),

          // Videos list
          _filteredVideos.isEmpty
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(32),
                    child: Text(
                      'No videos found',
                      style: TextStyle(color: Colors.grey, fontSize: 16),
                    ),
                  ),
                )
              : ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _filteredVideos.length,
                  itemBuilder: (context, index) {
                    return _buildVideoItem(_filteredVideos[index], index);
                  },
                ),

          const SizedBox(height: 100), // Extra space for bottom nav
        ],
      ),
    );
  }

  Widget _buildVideoItem(Video video, int index) {
    final isNew = index < 5; // Mark first 5 as new
    final categoryName = widget.categories
        .firstWhere(
          (cat) => cat.id == video.categoryId,
          orElse: () => Category(
            id: '',
            name: 'General',
            description: '',
            order: 0,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        )
        .name;

    return GestureDetector(
      onTap: () async {
        if (video.isPremium) {
          // Check if user has access to premium content (includes test user check)
          final hasAccess = await NewSubscriptionService.hasAccessToPremiumContent();

          if (!hasAccess) {
            if (mounted) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const UnifiedSubscriptionScreen(),
                ),
              );
            }
            return;
          }
        }

        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => VideoPlayerScreen(
                video: video,
                playlist: widget.allVideos.where((v) => v.categoryId == video.categoryId).toList(),
                initialIndex: widget.allVideos.where((v) => v.categoryId == video.categoryId).toList().indexOf(video),
              ),
            ),
          );
        }
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        child: Row(
          children: [
            // Thumbnail
            Container(
              width: 120,
              height: 80,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: _getGradientColors(index),
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      Icons.play_circle_fill,
                      color: Colors.white.withValues(alpha: 0.8),
                      size: 32,
                    ),
                  ),
                  Positioned(
                    bottom: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '${video.duration ~/ 60}:${(video.duration % 60).toString().padLeft(2, '0')}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                  if (isNew)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'TODAY',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  if (video.isPremium)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.lock,
                          color: Color(0xFFFFD700),
                          size: 14,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // Video info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          video.title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      IconButton(
                        onPressed: () async {
                          final success = await UserService.addToFavorites(video.id);
                          if (success && mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Added to favorites'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          } else if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Failed to add to favorites'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        icon: const Icon(
                          Icons.bookmark_border,
                          color: Colors.grey,
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    categoryName,
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Color> _getGradientColors(int index) {
    final gradients = [
      [const Color(0xFF6366F1), const Color(0xFF8B5CF6)],
      [const Color(0xFFEC4899), const Color(0xFFF97316)],
      [const Color(0xFF10B981), const Color(0xFF059669)],
      [const Color(0xFFF59E0B), const Color(0xFFEF4444)],
      [const Color(0xFF3B82F6), const Color(0xFF1D4ED8)],
    ];
    return gradients[index % gradients.length];
  }
}
