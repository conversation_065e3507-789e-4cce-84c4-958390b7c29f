import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../services/google_signin_service.dart';
import '../services/auth_service.dart';
import '../services/new_subscription_service.dart';
import '../models/auth_response.dart';
import 'onboarding_screen.dart';
import 'subscription_intro_screen.dart';
import 'main_content_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> with TickerProviderStateMixin {
  bool _isLoading = false;
  String? _errorMessage;

  // Hidden email login feature
  int _logoClickCount = 0;
  bool _showEmailLogin = false;
  bool _isEmailLoading = false;
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeGoogleSignIn();
    _checkExistingLogin();

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _initializeGoogleSignIn() async {
    await GoogleSignInService.initialize();
  }

  Future<void> _checkExistingLogin() async {
    final isLoggedIn = await AuthService.isLoggedIn();
    if (isLoggedIn && mounted) {
      // Get user data to check onboarding and premium status
      final user = await AuthService.getStoredUser();

      if (user != null) {
        // Check if user has completed onboarding (either flag is true or has essential data)
        final hasEssentialData = user.phoneNumber != null &&
                                 user.phoneNumber!.isNotEmpty &&
                                 user.classLevel != null &&
                                 user.age != null;

        if (user.isOnboardingComplete || hasEssentialData) {
          // User has completed onboarding, check premium status
          final hasPremiumAccess = await NewSubscriptionService.hasAccessToPremiumContent();

          if (mounted) {
            if (hasPremiumAccess) {
              // Premium users go directly to main content
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const MainContentScreen()),
              );
            } else {
              // Free users see subscription intro
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const SubscriptionIntroScreen()),
              );
            }
          }
        } else if (mounted) {
          // User needs to complete onboarding
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => OnboardingScreen(user: user)),
          );
        }
      }
    }
  }

  Future<void> _signInWithGoogle() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Sign in with Google
      final GoogleSignInAccount? account = await GoogleSignInService.signIn();

      if (account == null) {
        setState(() {
          _errorMessage = 'Google sign-in was cancelled';
          _isLoading = false;
        });
        return;
      }

      // Get ID token
      final String? idToken = await GoogleSignInService.getIdToken(account);
      // print("12233$idToken");R

      if (idToken == null) {
        setState(() {
          _errorMessage = 'Failed to get authentication token';
          _isLoading = false;
        });
        return;
      }

      // Authenticate with backend
      final AuthResponse authResponse = await AuthService.authenticateWithGoogle(idToken);

      if (authResponse.success && authResponse.data != null) {
        // Check if onboarding is complete
        final user = authResponse.data!.user;
        if (mounted) {
          // Check if user has completed onboarding (either flag is true or has essential data)
          final hasEssentialData = user.phoneNumber != null &&
                                   user.phoneNumber!.isNotEmpty &&
                                   user.classLevel != null &&
                                   user.age != null;

          if (user.isOnboardingComplete || hasEssentialData) {
            // User has completed onboarding, check premium status
            final hasPremiumAccess = await NewSubscriptionService.hasAccessToPremiumContent();

            if (mounted) {
              if (hasPremiumAccess) {
                // Premium users go directly to main content
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(builder: (context) => const MainContentScreen()),
                );
              } else {
                // Free users see subscription intro first
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(builder: (context) => const SubscriptionIntroScreen()),
                );
              }
            }
          } else {
            // Navigate to onboarding screen
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => OnboardingScreen(user: user)),
            );
          }
        }
      } else {
        print("34455$authResponse.message");
        setState(() {
          _errorMessage = authResponse.message;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Sign-in failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _onLogoTap() {
    setState(() {
      _logoClickCount++;
    });

    if (_logoClickCount > 9 && !_showEmailLogin) {
      setState(() {
        _showEmailLogin = true;
      });
      _animationController.forward();

      // Show a subtle feedback
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text(
            '🔓 Developer login unlocked!',
            style: TextStyle(color: Colors.white),
          ),
          backgroundColor: const Color(0xFFFFD700),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  void _toggleLoginMode() {
    setState(() {
      _showEmailLogin = !_showEmailLogin;
      _errorMessage = null;
    });

    if (_showEmailLogin) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  Future<void> _signInWithEmail() async {
    if (_emailController.text.trim().isEmpty || _passwordController.text.trim().isEmpty) {
      setState(() {
        _errorMessage = 'Please enter both email and password';
      });
      return;
    }

    setState(() {
      _isEmailLoading = true;
      _errorMessage = null;
    });

    try {
      // Authenticate with backend using email and password
      final AuthResponse authResponse = await AuthService.authenticateWithEmail(
        _emailController.text.trim(),
        _passwordController.text.trim(),
      );

      if (authResponse.success && authResponse.data != null) {
        // Check if onboarding is complete
        final user = authResponse.data!.user;
        if (mounted) {
          // Check if user has completed onboarding (either flag is true or has essential data)
          final hasEssentialData = user.phoneNumber != null &&
                                   user.phoneNumber!.isNotEmpty &&
                                   user.classLevel != null &&
                                   user.age != null;

          if (user.isOnboardingComplete || hasEssentialData) {
            // User has completed onboarding, check premium status
            final hasPremiumAccess = await NewSubscriptionService.hasAccessToPremiumContent();

            if (mounted) {
              if (hasPremiumAccess) {
                // Premium users go directly to main content
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(builder: (context) => const MainContentScreen()),
                );
              } else {
                // Free users see subscription intro first
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(builder: (context) => const SubscriptionIntroScreen()),
                );
              }
            }
          } else {
            // Navigate to onboarding screen
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => OnboardingScreen(user: user)),
            );
          }
        }
      } else {
        setState(() {
          _errorMessage = authResponse.message ?? 'Invalid email or password';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Login failed: $e';
      });
    } finally {
      setState(() {
        _isEmailLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A), // Dark background matching app theme
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // App Logo (Clickable)
              Center(
                child: GestureDetector(
                  onTap: _onLogoTap,
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(60),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFFFFD700).withValues(alpha: _logoClickCount > 5 ? 0.5 : 0.3),
                          blurRadius: _logoClickCount > 5 ? 25 : 20,
                          spreadRadius: _logoClickCount > 5 ? 8 : 5,
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: Image.asset(
                        'assets/images/English Guru_Logo.png',
                        width: 120,
                        height: 120,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              color: const Color(0xFFFFD700),
                              borderRadius: BorderRadius.circular(60),
                            ),
                            child: const Icon(
                              Icons.school,
                              color: Colors.black,
                              size: 60,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 32),

              // App Title
              const Text(
                'English Guru ',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFFFD700), // Gold color matching app theme
                  letterSpacing: 1.2,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Learn, Grow, Succeed',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                  letterSpacing: 0.5,
                ),
              ),
              const SizedBox(height: 48),

              // Animated content switcher
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 500),
                child: _showEmailLogin ? _buildEmailLoginForm() : _buildGoogleLoginForm(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGoogleLoginForm() {
    return Column(
      key: const ValueKey('google_login'),
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Welcome text
        const Text(
          'Welcome Back!',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Sign in to continue your learning journey',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color: Colors.white70,
          ),
        ),
        const SizedBox(height: 48),

        // Error message
        if (_errorMessage != null)
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(bottom: 24),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: Colors.red.shade400,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _errorMessage!,
                    style: TextStyle(
                      color: Colors.red.shade400,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),

        // Google Sign-In Button
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [
                const Color(0xFFFFD700),
                const Color(0xFFFFD700).withValues(alpha: 0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFFFD700).withValues(alpha: 0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _signInWithGoogle,
            icon: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                    ),
                  )
                : Image.asset(
                    'assets/google_logo.png',
                    width: 24,
                    height: 24,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(
                        Icons.login,
                        color: Colors.black,
                        size: 24,
                      );
                    },
                  ),
            label: Text(
              _isLoading ? 'Signing in...' : 'Continue with Google',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              foregroundColor: Colors.black,
              shadowColor: Colors.transparent,
              padding: const EdgeInsets.symmetric(vertical: 18),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: 0,
            ),
          ),
        ),

        const SizedBox(height: 32),

        // Terms and Privacy
        Text(
          'By continuing, you agree to our Terms of Service and Privacy Policy',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 12,
            color: Colors.white.withValues(alpha: 0.6),
            height: 1.4,
          ),
        ),
      ],
    );
  }

  Widget _buildEmailLoginForm() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Column(
        key: const ValueKey('email_login'),
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Developer Login Header
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.developer_mode,
                color: Color(0xFFFFD700),
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text(
                'Developer Login',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'Admin access for development purposes',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 32),

          // Error message
          if (_errorMessage != null)
            Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.only(bottom: 24),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red.shade400,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: TextStyle(
                        color: Colors.red.shade400,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Email TextField
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: const Color(0xFF2A2A2A),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.1),
              ),
            ),
            child: TextField(
              controller: _emailController,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Email Address',
                hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.6)),
                prefixIcon: const Icon(
                  Icons.email_outlined,
                  color: Color(0xFFFFD700),
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 18,
                ),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ),

          const SizedBox(height: 16),

          // Password TextField
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: const Color(0xFF2A2A2A),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.1),
              ),
            ),
            child: TextField(
              controller: _passwordController,
              style: const TextStyle(color: Colors.white),
              obscureText: true,
              decoration: InputDecoration(
                hintText: 'Password',
                hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.6)),
                prefixIcon: const Icon(
                  Icons.lock_outline,
                  color: Color(0xFFFFD700),
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 18,
                ),
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Login Button
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                colors: [
                  const Color(0xFFFFD700),
                  const Color(0xFFFFD700).withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFFFD700).withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: _isEmailLoading ? null : _signInWithEmail,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Colors.black,
                shadowColor: Colors.transparent,
                padding: const EdgeInsets.symmetric(vertical: 18),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                elevation: 0,
              ),
              child: _isEmailLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                      ),
                    )
                  : const Text(
                      'Sign In',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
            ),
          ),

          const SizedBox(height: 24),

          // Back to Google Login
          TextButton(
            onPressed: _toggleLoginMode,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.arrow_back,
                  color: Color(0xFFFFD700),
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  'Back to Google Login',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Demo credentials hint
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFFFFD700).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFFFFD700).withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              children: [
                Text(
                  '💡 Demo Credentials',
                  style: TextStyle(
                    color: const Color(0xFFFFD700),
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Email: <EMAIL>\nPassword: praveen@123',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 11,
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
