import 'package:flutter/material.dart';
import '../models/user.dart';
import '../models/module_models.dart';
import '../services/auth_service.dart';
import '../services/modules_service.dart';
import '../utils/premium_content_helper.dart';
import 'profile_screen.dart';
import 'enhanced_profile_screen.dart';
import 'module_content_viewer.dart';

class NewHomeScreen extends StatefulWidget {
  const NewHomeScreen({super.key});

  @override
  State<NewHomeScreen> createState() => _NewHomeScreenState();
}

class _NewHomeScreenState extends State<NewHomeScreen> {
  User? _currentUser;
  bool _isLoading = true;
  final ScrollController _scrollController = ScrollController();
  String _currentUnit = 'Unit 1 | Day 1';
  bool _isDrawerOpen = false;

  // Dynamic data
  List<LearningModule> _currentModules = [];
  Map<int, List<LearningModule>> _allModules = {};
  int _userClass = 1;
  bool _isLoadingModules = false;

  // Color animation
  int _currentColorIndex = 0;

  // Define different colors for different units
  final List<Color> _unitColors = [
    const Color(0xFF1A1A2E), // Original dark purple
    const Color(0xFF2D1B69), // Deep purple
    const Color(0xFF1B2951), // Dark blue
    const Color(0xFF0F3460), // Navy blue
    const Color(0xFF16213E), // Dark slate
    const Color(0xFF533483), // Purple
    const Color(0xFF7209B7), // Bright purple
    const Color(0xFF2D4A22), // Dark green
    const Color(0xFF5D4E75), // Muted purple
  ];

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _loadModulesData();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Update current unit based on scroll position and available modules
    if (_currentModules.isEmpty) return;

    double offset = _scrollController.offset;
    int moduleIndex = 0;

    // Calculate which module is currently visible based on scroll position
    // Each module section is approximately 350px tall (adjusted for better responsiveness)
    if (offset > 150) { // Start after header (reduced threshold)
      moduleIndex = ((offset - 150) / 350).floor();
      moduleIndex = moduleIndex.clamp(0, _currentModules.length - 1);
    }

    // Update color based on scroll position and module index
    int newColorIndex = moduleIndex.clamp(0, _unitColors.length - 1);

    if (moduleIndex < _currentModules.length) {
      final currentModule = _currentModules[moduleIndex];
      final newUnit = 'Unit ${moduleIndex + 1} | ${currentModule.title}';

      if (_currentUnit != newUnit || _currentColorIndex != newColorIndex) {
        setState(() {
          _currentUnit = newUnit;
          _currentColorIndex = newColorIndex;
        });
      }
    } else {
      // Handle case when scrolling beyond available modules
      if (_currentColorIndex != 0) {
        setState(() {
          _currentColorIndex = 0;
        });
      }
    }
  }

  Future<void> _loadUserData() async {
    try {
      final user = await AuthService.getStoredUser();
      setState(() {
        _currentUser = user;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading user data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadModulesData() async {
    setState(() {
      _isLoadingModules = true;
    });

    try {
      // Get user's class and all modules
      _userClass = await ModulesService.getUserClass();
      debugPrint('🎓 USER CLASS: $_userClass');

      final allModules = await ModulesService.getModulesGroupedByClass();
      final currentModules = await ModulesService.getLearningModulesByClass(_userClass);

      debugPrint('📚 MODULES LOADED:');
      debugPrint('📊 Total modules for class $_userClass: ${currentModules.length}');

      for (int i = 0; i < currentModules.length; i++) {
        final module = currentModules[i];
        debugPrint('📖 Module ${i + 1}: ${module.title}');
        debugPrint('   📝 Content count: ${module.content.length}');

        for (int j = 0; j < module.content.length; j++) {
          final content = module.content[j];
          debugPrint('   📄 Content ${j + 1}: ${content.contentType} (ID: ${content.contentId})');

          if (['summary', 'reading', 'instructions', 'notes', 'text'].contains(content.contentType.toLowerCase())) {
            debugPrint('   🎯 TEXT CONTENT FOUND! Type: ${content.contentType}, ID: ${content.contentId}');
          }
        }
      }

      setState(() {
        _allModules = allModules;
        _currentModules = currentModules;
        _currentUnit = 'Class $_userClass | ${currentModules.length} lessons';
        _isLoadingModules = false;
      });
    } catch (e) {
      debugPrint('💥 ERROR loading modules: $e');
      setState(() {
        _isLoadingModules = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E), // Keep background black/dark
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Stack(
                children: [
                  // Main scrollable content
                  CustomScrollView(
                    controller: _scrollController,
                    slivers: [
                      // Space for name header and sticky top bar
                      const SliverToBoxAdapter(
                        child: SizedBox(height: 120),
                      ),

                      // Main content starts here
                      SliverToBoxAdapter(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Container(), // Empty container as content starts below
                        ),
                      ),

                      // Lessons with thread architecture
                      SliverToBoxAdapter(
                        child: _buildLessonsWithThread(),
                      ),

                      // Extra space for bottom nav
                      const SliverToBoxAdapter(
                        child: SizedBox(height: 100),
                      ),
                    ],
                  ),

                  // Sticky top bar
                  _buildStickyTopBar(),

                  // Profile avatar button (separate positioned widget)
                  _buildProfileButton(),

                  // Backdrop overlay
                  if (_isDrawerOpen)
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _isDrawerOpen = false;
                        });
                      },
                      child: Container(
                        color: Colors.black.withValues(alpha: 0.5),
                      ),
                    ),

                  // Right-side drawer
                  if (_isDrawerOpen) _buildRightDrawer(),
                ],
              ),
      ),
    );
  }

  Widget _buildRightDrawer() {
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      top: 0,
      right: 0,
      bottom: 0,
      width: MediaQuery.of(context).size.width * 0.85,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF1A1A2E),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.5),
              blurRadius: 10,
              offset: const Offset(-2, 0),
            ),
          ],
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFF2D1B69),
                border: Border(
                  bottom: BorderSide(
                    color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  const Text(
                    'Units & Lessons',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _isDrawerOpen = false;
                      });
                    },
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Expanded(
              child: _isLoadingModules
                  ? const Center(
                      child: CircularProgressIndicator(
                        color: Color(0xFF6C5CE7),
                      ),
                    )
                  : _allModules.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.school_outlined,
                                size: 64,
                                color: Colors.grey.withValues(alpha: 0.5),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No modules available',
                                style: TextStyle(
                                  color: Colors.grey.withValues(alpha: 0.7),
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView(
                          padding: const EdgeInsets.all(20),
                          children: _buildDynamicDrawerContent(),
                        ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildDynamicDrawerContent() {
    List<Widget> widgets = [];

    // Sort classes and build sections
    final sortedClasses = _allModules.keys.toList()..sort();

    for (int i = 0; i < sortedClasses.length; i++) {
      final classNumber = sortedClasses[i];
      final modules = _allModules[classNumber]!;

      // Add class section
      widgets.add(_buildDrawerClassSection(classNumber, modules));

      // Add spacing between classes
      if (i < sortedClasses.length - 1) {
        widgets.add(const SizedBox(height: 20));
      }
    }

    return widgets;
  }

  Widget _buildDrawerClassSection(int classNumber, List<LearningModule> modules) {
    final isCurrentClass = classNumber == _userClass;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isCurrentClass
                ? const Color(0xFF6C5CE7)
                : const Color(0xFF6C5CE7).withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(20),
            border: isCurrentClass
                ? Border.all(color: Colors.white.withValues(alpha: 0.3), width: 1)
                : null,
          ),
          child: Row(
            children: [
              if (isCurrentClass)
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.play_arrow,
                    color: Color(0xFF6C5CE7),
                    size: 12,
                  ),
                ),
              Expanded(
                child: Text(
                  'Class $classNumber',
                  style: TextStyle(
                    color: isCurrentClass ? Colors.white : Colors.white70,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Text(
                '${modules.length} ${modules.length == 1 ? 'lesson' : 'lessons'}',
                style: TextStyle(
                  color: isCurrentClass ? Colors.white70 : Colors.white60,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        ...modules.map((module) => _buildDrawerLessonItem(module, isCurrentClass)),
      ],
    );
  }

  Widget _buildDrawerLessonItem(LearningModule module, bool isCurrentClass) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: GestureDetector(
        onTap: () {
          setState(() {
            _isDrawerOpen = false;
          });
          _navigateToModule(module);
        },
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: const Color(0xFF6C5CE7).withValues(alpha: 0.1),
                border: Border.all(
                  color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Center(
                child: Text(
                  module.contentTypeIcon,
                  style: const TextStyle(fontSize: 20),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    module.title,
                    style: TextStyle(
                      color: isCurrentClass ? Colors.white : Colors.white70,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      Text(
                        module.topic.title,
                        style: TextStyle(
                          color: Colors.grey.withValues(alpha: 0.6),
                          fontSize: 11,
                        ),
                      ),
                      if (module.estimatedDuration > 0) ...[
                        Text(
                          ' • ${module.estimatedDuration} min',
                          style: TextStyle(
                            color: Colors.grey.withValues(alpha: 0.6),
                            fontSize: 11,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
            if (module.isPremium && !(module.hasAccess ?? false))
              PremiumContentHelper.buildPremiumIndicator(
                iconSize: 12,
                showText: false,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildUnitSection({
    required String unitTitle,
    required String lessonCount,
    required List<Widget> lessons,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: const Color(0xFF6C5CE7),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  unitTitle,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Text(
                lessonCount,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        ...lessons,
      ],
    );
  }

  Widget _buildLessonItem({
    required String title,
    required String imagePath,
    required bool isCompleted,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: DecorationImage(
                image: AssetImage(imagePath),
                fit: BoxFit.cover,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (isCompleted)
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 20,
            ),
        ],
      ),
    );
  }

  Widget _buildHeaderNameOnly() {
    return Row(
      children: [
        Expanded(
          child: Text(
            'Hey ${_currentUser?.name.split(' ').first ?? 'Praveen'}!',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A3E),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.local_fire_department,
                color: Colors.orange,
                size: 16,
              ),
              const SizedBox(width: 4),
              const Text(
                '0',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            'Hey ${_currentUser?.name.split(' ').first ?? 'Praveen'}!',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFF2A2A3E),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.1),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.local_fire_department,
                    color: Colors.orange,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  const Text(
                    '0',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: () {
                  print('🔥 Profile avatar tapped! Navigating to profile...');
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const EnhancedProfileScreen(),
                    ),
                  );
                },
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: const LinearGradient(
                      colors: [Color(0xFF6C5CE7), Color(0xFF2D1B69)],
                    ),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.4),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: _currentUser?.profilePicture != null
                      ? ClipOval(
                          child: Image.network(
                            _currentUser!.profilePicture!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                _buildDefaultAvatar(),
                          ),
                        )
                      : _buildDefaultAvatar(),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDefaultAvatar() {
    return Container(
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          colors: [Color(0xFF6C5CE7), Color(0xFF2D1B69)],
        ),
      ),
      child: Center(
        child: Text(
          (_currentUser?.name?.isNotEmpty == true)
              ? _currentUser!.name!.substring(0, 1).toUpperCase()
              : 'U',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildProfileButton() {
    return Positioned(
      top: 20,
      right: 20,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            print('🔥 Profile button tapped! Navigating to profile...');
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const EnhancedProfileScreen(),
              ),
            );
          },
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: const LinearGradient(
                colors: [Color(0xFF6C5CE7), Color(0xFF2D1B69)],
              ),
              border: Border.all(
                color: Colors.white,
                width: 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF6C5CE7).withValues(alpha: 0.5),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
                BoxShadow(
                  color: Colors.white.withValues(alpha: 0.3),
                  blurRadius: 6,
                  offset: const Offset(0, 0),
                ),
              ],
            ),
            child: _currentUser?.profilePicture != null
                ? ClipOval(
                    child: Image.network(
                      _currentUser!.profilePicture!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          _buildDefaultAvatar(),
                    ),
                  )
                : _buildDefaultAvatar(),
          ),
        ),
      ),
    );
  }

  Widget _buildStickyTopBar() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF1A1A2E).withValues(alpha: 0.95),
          border: Border(
            bottom: BorderSide(
              color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
              width: 1,
            ),
          ),
        ),
        child: Column(
          children: [
            // Header with name only (profile is separate)
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 20, 70, 20), // Extra right padding for profile button
              child: _buildHeaderNameOnly(),
            ),

            // Sticky unit bar
            GestureDetector(
              onTap: () {
                setState(() {
                  _isDrawerOpen = !_isDrawerOpen;
                });
              },
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                decoration: BoxDecoration(
                  color: const Color(0xFF2D1B69).withValues(alpha: 0.95),
                  border: Border(
                    bottom: BorderSide(
                      color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: const Color(0xFF6C5CE7),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Text(
                        _currentUnit,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Icon(
                      _isDrawerOpen ? Icons.close : Icons.bookmark_outline,
                      color: Colors.white.withValues(alpha: 0.7),
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedLessonCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF6C63FF), Color(0xFF9C88FF)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF6C63FF),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Unit 2 | Day 2, 3, 4',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      'IELTS Speaking Part 2',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.folder,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),

          // Image section
          Container(
            height: 200,
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              image: const DecorationImage(
                image: NetworkImage('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=200&fit=crop'),
                fit: BoxFit.cover,
              ),
            ),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.6),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.6),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.visibility,
                                color: Colors.white,
                                size: 12,
                              ),
                              const SizedBox(width: 4),
                              const Text(
                                '+39.9k',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    const Text(
                      'Day 2 • ▶ Exercise',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      'IELTS Part 2',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Start lesson button
          Padding(
            padding: const EdgeInsets.all(20),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // TODO: Navigate to lesson
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Starting IELTS Part 2 lesson...'),
                      backgroundColor: Color(0xFF6C63FF),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6C63FF),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: const Text(
                  'Start lesson',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLessonsWithThread() {
    if (_isLoadingModules) {
      return const Padding(
        padding: EdgeInsets.all(50),
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_currentModules.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(50),
        child: Center(
          child: Column(
            children: [
              Icon(
                Icons.school_outlined,
                size: 64,
                color: Colors.grey.withValues(alpha: 0.5),
              ),
              const SizedBox(height: 16),
              Text(
                'No lessons available for Class $_userClass',
                style: TextStyle(
                  color: Colors.grey.withValues(alpha: 0.7),
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          const SizedBox(height: 30),
          ..._buildDynamicLessons(),
        ],
      ),
    );
  }

  List<Widget> _buildDynamicLessons() {
    List<Widget> widgets = [];

    for (int i = 0; i < _currentModules.length; i++) {
      final module = _currentModules[i];

      // Add unit header
      widgets.add(_buildDynamicUnitHeader(module, i + 1));
      widgets.add(const SizedBox(height: 20));

      // Add lesson card with thread
      widgets.add(_buildDynamicLessonWithThread(module, i));

      // Add spacing between lessons
      if (i < _currentModules.length - 1) {
        widgets.add(const SizedBox(height: 30));
      }
    }

    return widgets;
  }

  Widget _buildDynamicUnitHeader(LearningModule module, int unitNumber) {
    // Get the current color for this unit
    Color currentUnitColor = _unitColors[(unitNumber - 1).clamp(0, _unitColors.length - 1)];
    Color lighterColor = Color.lerp(currentUnitColor, Colors.white, 0.2) ?? currentUnitColor;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 600),
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [currentUnitColor, lighterColor],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: currentUnitColor.withValues(alpha: 0.4),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Unit $unitNumber | ${module.topic.title}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            module.title,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${module.totalContentCount} ${module.totalContentCount == 1 ? 'lesson' : 'lessons'}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${module.estimatedDuration} min',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                module.contentTypeIcon,
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDynamicLessonWithThread(LearningModule module, int index) {
    // Get the current color for this unit
    Color currentUnitColor = _unitColors[index.clamp(0, _unitColors.length - 1)];

    return Stack(
      children: [
        // Thread line with dynamic color
        if (index < _currentModules.length - 1)
          Positioned(
            left: 30,
            top: 120,
            bottom: -30,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 600),
              width: 2,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    currentUnitColor.withValues(alpha: 0.8),
                    currentUnitColor.withValues(alpha: 0.3),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
          ),

        // Lesson card with dynamic color
        AnimatedContainer(
          duration: const Duration(milliseconds: 600),
          margin: const EdgeInsets.only(left: 60),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A3E),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: currentUnitColor.withValues(alpha: 0.3),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: currentUnitColor.withValues(alpha: 0.2),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Content preview with dynamic color
              AnimatedContainer(
                duration: const Duration(milliseconds: 600),
                height: 120,
                decoration: BoxDecoration(
                  color: currentUnitColor.withValues(alpha: 0.15),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        module.contentTypeIcon,
                        style: const TextStyle(fontSize: 32),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        ModulesService.getContentTypeDisplayName(module.primaryContentType),
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Content details
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            module.title,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        if (module.isPremium && !(module.hasAccess ?? false))
                          PremiumContentHelper.buildPremiumIndicator(
                            iconSize: 14,
                            fontSize: 10,
                            showText: true,
                          ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      module.description,
                      style: TextStyle(
                        color: Colors.grey.withValues(alpha: 0.8),
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 12),

                    // Content breakdown
                    if (module.content.isNotEmpty) ...[
                      Wrap(
                        spacing: 8,
                        runSpacing: 4,
                        children: module.content.take(3).map((content) {
                          return AnimatedContainer(
                            duration: const Duration(milliseconds: 600),
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: currentUnitColor.withValues(alpha: 0.25),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              '${ModulesService.getContentTypeIcon(content.contentType)} ${content.customTitle ?? ModulesService.getContentTypeDisplayName(content.contentType)}',
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 10,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                      if (module.content.length > 3)
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            '+${module.content.length - 3} more',
                            style: TextStyle(
                              color: Colors.grey.withValues(alpha: 0.6),
                              fontSize: 10,
                            ),
                          ),
                        ),
                      const SizedBox(height: 16),
                    ],

                    // Action button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          _navigateToModule(module);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF6C5CE7),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          module.isPremium && !(module.hasAccess ?? false)
                              ? '🔒 Premium Content'
                              : 'Start Learning',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Thread dot with dynamic color
        Positioned(
          left: 24,
          top: 60,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 600),
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: currentUnitColor,
              shape: BoxShape.circle,
              border: Border.all(
                color: const Color(0xFF1A1A2E),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: currentUnitColor.withValues(alpha: 0.4),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _navigateToModule(LearningModule module) async {
    // Check premium access before navigating to module
    final hasAccess = await PremiumContentHelper.handleModuleAccess(
      context: context,
      isPremium: module.isPremium,
      moduleTitle: module.title,
      useUnifiedScreen: true,
      popCurrentScreen: false,
    );

    if (!hasAccess) {
      return; // User was redirected to subscription screen
    }

    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ModuleContentViewer(module: module),
        ),
      );
    }
  }

  Widget _buildUnitHeader(String unit, String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF2D1B69),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  unit,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.bookmark_outline,
            color: Colors.white.withValues(alpha: 0.7),
            size: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildMainLessonWithThread() {
    return Stack(
      children: [
        // Thread line
        Positioned(
          left: 20,
          top: 0,
          bottom: 0,
          child: Container(
            width: 2,
            color: const Color(0xFF6C5CE7).withValues(alpha: 0.5),
          ),
        ),

        // Thread dot
        Positioned(
          left: 11,
          top: 20,
          child: Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: const Color(0xFF6C5CE7),
              shape: BoxShape.circle,
              border: Border.all(
                color: const Color(0xFF1A1A2E),
                width: 3,
              ),
            ),
          ),
        ),

        // Lesson card
        Padding(
          padding: const EdgeInsets.only(left: 50),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF6C63FF), Color(0xFF9C88FF)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                // Image section
                Container(
                  height: 200,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                    image: DecorationImage(
                      image: NetworkImage('https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400&h=200&fit=crop'),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.7),
                        ],
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.visibility,
                                  color: Colors.white,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                const Text(
                                  '+39.9k',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Content section
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'Day 1',
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Icon(
                            Icons.play_arrow,
                            color: Colors.white70,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          const Text(
                            'Exercise',
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'IELTS Part 1',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            // TODO: Navigate to lesson
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Starting IELTS Part 1 lesson...'),
                              ),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF6C5CE7),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text(
                            'Start lesson',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSecondaryLessonsWithThread() {
    return Stack(
      children: [
        // Thread line
        Positioned(
          left: 20,
          top: 0,
          bottom: 0,
          child: Container(
            width: 2,
            color: const Color(0xFF6C5CE7).withValues(alpha: 0.5),
          ),
        ),

        // Lessons column
        Padding(
          padding: const EdgeInsets.only(left: 50),
          child: Column(
            children: [
              _buildThreadLessonCard(
                title: 'IELTS Part 2',
                subtitle: 'Day 2 • ▶ Exercise',
                isLocked: true,
                imagePath: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=200&fit=crop',
                threadPosition: 20,
              ),
              const SizedBox(height: 20),
              _buildThreadLessonCard(
                title: 'Tongue Twister',
                subtitle: '',
                isLocked: true,
                imagePath: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400&h=200&fit=crop',
                threadPosition: 20,
              ),
              const SizedBox(height: 20),
              _buildThreadLessonCard(
                title: 'Day 1: Revision',
                subtitle: '',
                isLocked: true,
                imagePath: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=200&fit=crop',
                threadPosition: 20,
              ),
            ],
          ),
        ),

        // Thread dots
        Positioned(
          left: 11,
          top: 20,
          child: Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: const Color(0xFF6C5CE7),
              shape: BoxShape.circle,
              border: Border.all(
                color: const Color(0xFF1A1A2E),
                width: 3,
              ),
            ),
          ),
        ),
        Positioned(
          left: 11,
          top: 200,
          child: Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: const Color(0xFF6C5CE7),
              shape: BoxShape.circle,
              border: Border.all(
                color: const Color(0xFF1A1A2E),
                width: 3,
              ),
            ),
          ),
        ),
        Positioned(
          left: 11,
          top: 380,
          child: Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: const Color(0xFF6C5CE7),
              shape: BoxShape.circle,
              border: Border.all(
                color: const Color(0xFF1A1A2E),
                width: 3,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildThreadLessonCard({
    required String title,
    required String subtitle,
    required bool isLocked,
    required String imagePath,
    required double threadPosition,
  }) {
    return Container(
      height: 160,
      decoration: BoxDecoration(
        color: const Color(0xFF2D1B69),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Image section
          Container(
            width: 120,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                bottomLeft: Radius.circular(16),
              ),
              image: DecorationImage(
                image: NetworkImage(imagePath),
                fit: BoxFit.cover,
              ),
            ),
          ),

          // Content section
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (subtitle.isNotEmpty) ...[
                    Text(
                      subtitle,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 8),
                  ],
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: isLocked
                          ? const Color(0xFF6C5CE7).withValues(alpha: 0.3)
                          : const Color(0xFF6C5CE7),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          isLocked ? Icons.lock : Icons.play_arrow,
                          color: Colors.white,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          isLocked ? 'Locked' : 'Start lesson',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Avatar section
          Padding(
            padding: const EdgeInsets.all(16),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                image: DecorationImage(
                  image: NetworkImage(imagePath),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLessonCard({
    required String title,
    required bool isLocked,
    required String imagePath,
  }) {
    return Container(
      width: double.infinity,
      height: 120,
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A3E),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
        ),
      ),
      child: Stack(
        children: [
          // Background image
          ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: NetworkImage(imagePath),
                  fit: BoxFit.cover,
                ),
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  gradient: LinearGradient(
                    colors: [
                      Colors.black.withValues(alpha: 0.6),
                      Colors.black.withValues(alpha: 0.8),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
              ),
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                // Progress indicator (empty circle)
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.5),
                      width: 2,
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // Title
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                // Profile image
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    image: DecorationImage(
                      image: NetworkImage(
                        title.contains('Tongue')
                          ? 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face'
                          : 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
                      ),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Locked overlay
          if (isLocked)
            Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: Colors.black.withValues(alpha: 0.7),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.lock,
                      color: Colors.white,
                      size: 32,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Locked',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

}
