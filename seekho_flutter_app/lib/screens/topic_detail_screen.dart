import 'package:flutter/material.dart';
import '../models/category.dart';
import '../models/topic.dart';
import '../models/video.dart';
import '../services/content_service.dart';
import 'video_detail_screen.dart';
import 'unified_subscription_screen.dart';
import '../widgets/premium_lock_icon.dart';
import '../services/new_subscription_service.dart';

class TopicDetailScreen extends StatefulWidget {
  final Topic topic;
  final Category category;

  const TopicDetailScreen({
    super.key,
    required this.topic,
    required this.category,
  });

  @override
  State<TopicDetailScreen> createState() => _TopicDetailScreenState();
}

class _TopicDetailScreenState extends State<TopicDetailScreen> {
  List<Video> _videos = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTopicVideos();
  }

  Future<void> _loadTopicVideos() async {
    setState(() => _isLoading = true);

    try {
      final videosResponse = await ContentService.getVideosInTopic(widget.topic.id);

      setState(() {
        _videos = videosResponse.data ?? [];
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading videos: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF1A1A1A),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.topic.name,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () {
              // TODO: Implement search functionality
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadTopicVideos,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Topic description if available
                    if (widget.topic.description.isNotEmpty) ...[
                      Text(
                        widget.topic.description,
                        style: TextStyle(
                          color: Colors.grey[300],
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],

                    // Videos section
                    _buildVideosSection(),

                    const SizedBox(height: 100), // Space for bottom nav
                  ],
                ),
              ),
            ),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  Widget _buildVideosSection() {
    if (_videos.isEmpty) {
      return const Center(
        child: Text(
          'No videos available in this topic',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(
              Icons.bar_chart,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Top ${_videos.length} in ${widget.topic.name}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 0.75,
          ),
          itemCount: _videos.length,
          itemBuilder: (context, index) {
            return _buildNumberedVideoCard(_videos[index], index + 1);
          },
        ),
      ],
    );
  }

  Widget _buildNumberedVideoCard(Video video, int number) {
    // Define gradient colors for variety
    final gradients = [
      [const Color(0xFF4CAF50), const Color(0xFF8BC34A)], // Green
      [const Color(0xFF00BCD4), const Color(0xFF009688)], // Teal
      [const Color(0xFFE91E63), const Color(0xFF9C27B0)], // Pink/Purple
      [const Color(0xFF4CAF50), const Color(0xFF66BB6A)], // Green variant
    ];

    final gradient = gradients[(number - 1) % gradients.length];

    return GestureDetector(
      onTap: () async {
        if (video.isPremium) {
          // Check if user has access to premium content (includes test user check)
          final hasAccess = await NewSubscriptionService.hasAccessToPremiumContent();

          if (!hasAccess) {
            if (mounted) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const UnifiedSubscriptionScreen(),
                ),
              );
            }
            return;
          }
        }

        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => VideoDetailScreen(
                video: video,
                category: widget.category,
                allVideos: _videos,
              ),
            ),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              gradient[0],
              gradient[1],
            ],
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Stack(
          children: [
            // Background pattern or image could go here

            // Premium lock overlay
            Positioned(
              top: 8,
              right: 8,
              child: PremiumLockIcon(
                isPremium: video.isPremium,
                size: 16,
                padding: const EdgeInsets.all(6),
                borderRadius: 20,
              ),
            ),

            // Content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Top section with number and label
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text(
                          'EARNING APP REVIEW',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const Spacer(),

                  // Video title
                  Text(
                    video.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 8),

                  // Number
                  Text(
                    number.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildBottomNavigation() {
    return Container(
      height: 80,
      decoration: const BoxDecoration(
        color: Color(0xFF2A2A2A),
        border: Border(
          top: BorderSide(color: Colors.grey, width: 0.2),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildNavItem(Icons.home, 'Home', true),
          _buildNavItem(Icons.fiber_new, 'New', false),
          _buildNavItem(Icons.video_library, 'My Library', false),
        ],
      ),
    );
  }

  Widget _buildNavItem(IconData icon, String label, bool isSelected) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          color: isSelected ? Colors.white : Colors.grey,
          size: 24,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}
