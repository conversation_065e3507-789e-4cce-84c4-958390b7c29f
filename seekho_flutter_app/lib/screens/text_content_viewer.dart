import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import '../models/module_models.dart';
import '../services/text_content_service.dart';
import '../services/progress_service.dart';
import '../utils/premium_content_helper.dart';

class TextContentViewer extends StatefulWidget {
  final ModuleContent content;

  const TextContentViewer({
    super.key,
    required this.content,
  });

  @override
  State<TextContentViewer> createState() => _TextContentViewerState();
}

class _TextContentViewerState extends State<TextContentViewer> {
  bool _isLoading = true;
  TextContentData? _textContentData;
  String? _errorMessage;
  double _fontSize = 16.0;
  bool _isDarkMode = true;
  DateTime _startTime = DateTime.now();
  ScrollController _scrollController = ScrollController();
  bool _hasRecordedProgress = false;

  @override
  void initState() {
    super.initState();
    _loadTextContent();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _recordFinalProgress();
    super.dispose();
  }

  void _onScroll() {
    // Record progress when user scrolls through content
    if (!_hasRecordedProgress && _scrollController.hasClients) {
      final scrollPercentage = _scrollController.offset / _scrollController.position.maxScrollExtent;
      if (scrollPercentage > 0.8) { // 80% scrolled
        _recordProgress(100.0);
        _hasRecordedProgress = true;
      } else if (scrollPercentage > 0.5) { // 50% scrolled
        _recordProgress(50.0);
      }
    }
  }

  Future<void> _recordProgress(double progressPercentage) async {
    final timeSpent = DateTime.now().difference(_startTime).inSeconds;

    await ProgressService.recordContentProgress(
      contentId: widget.content.contentId,
      contentType: ContentType.text,
      progressPercentage: progressPercentage,
      timeSpent: timeSpent,
      metadata: {
        'contentTitle': widget.content.contentData?.title ?? '',
        'contentType': widget.content.contentType,
        'readingTime': _textContentData?.estimatedReadingTime ?? 0,
      },
    );
  }

  void _recordFinalProgress() {
    if (!_hasRecordedProgress) {
      _recordProgress(100.0); // Mark as completed when leaving
    }
  }

  Future<void> _loadTextContent() async {
    try {
      debugPrint('🚀 STARTING TEXT CONTENT LOAD');
      debugPrint('🆔 Content ID: ${widget.content.contentId}');
      debugPrint('🏷️ Content Type: ${widget.content.contentType}');

      final textContent = await TextContentService.getTextContentById(widget.content.contentId);

      debugPrint('📦 TEXT CONTENT RESULT:');
      debugPrint('✅ Content received: ${textContent != null}');

      if (textContent != null) {
        debugPrint('📝 Title: ${textContent.title}');
        debugPrint('🔓 Has Access: ${textContent.hasAccess}');
        debugPrint('💎 Is Premium: ${textContent.isPremium}');
        debugPrint('📄 Content Length: ${textContent.content.length}');

        // Record view
        TextContentService.recordTextContentView(widget.content.contentId);
      } else {
        debugPrint('❌ Text content is NULL');
      }

      setState(() {
        _textContentData = textContent;
        _isLoading = false;
      });

      debugPrint('🎯 UI STATE UPDATED: loading=false, hasData=${textContent != null}');

    } catch (e) {
      debugPrint('💥 ERROR IN _loadTextContent: $e');
      debugPrint('📍 Error type: ${e.runtimeType}');
      debugPrint('📍 Stack trace: ${StackTrace.current}');

      String errorMessage = 'Failed to load content. ';
      if (e.toString().contains('SocketException') || e.toString().contains('TimeoutException')) {
        errorMessage += 'Please check your internet connection and try again.';
      } else if (e.toString().contains('FormatException')) {
        errorMessage += 'Invalid response format from server.';
      } else if (e.toString().contains('401') || e.toString().contains('403')) {
        errorMessage += 'Authentication required. Please log in again.';
      } else if (e.toString().contains('404')) {
        errorMessage += 'Content not found.';
      } else if (e.toString().contains('500')) {
        errorMessage += 'Server error. Please try again later.';
      } else {
        errorMessage += 'Please try again later.';
      }

      setState(() {
        _isLoading = false;
        _errorMessage = errorMessage;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _isDarkMode ? const Color(0xFF1A1A2E) : Colors.white,
      appBar: AppBar(
        backgroundColor: _isDarkMode ? const Color(0xFF2D1B69) : const Color(0xFF6C5CE7),
        foregroundColor: Colors.white,
        title: Text(
          widget.content.contentData?.title ?? 'Text Content',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.text_fields),
            onPressed: _showTextSettings,
          ),
          IconButton(
            icon: Icon(_isDarkMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: () {
              setState(() {
                _isDarkMode = !_isDarkMode;
              });
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF6C5CE7),
              ),
            )
          : _textContentData == null
              ? _buildErrorState()
              : !_textContentData!.hasAccess
                  ? _buildLockedContentState()
                  : _buildContentView(),
    );
  }

  Widget _buildLockedContentState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFF6C5CE7).withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.lock_outline,
                size: 64,
                color: const Color(0xFF6C5CE7),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Premium Content',
              style: TextStyle(
                color: _isDarkMode ? Colors.white : Colors.black87,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              _textContentData?.title ?? 'Content',
              style: TextStyle(
                color: const Color(0xFF6C5CE7),
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'This content is available with a premium subscription. Upgrade now to access all learning materials and unlock your full potential!',
              style: TextStyle(
                color: _isDarkMode ? Colors.grey.withValues(alpha: 0.8) : Colors.grey[600],
                fontSize: 16,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      // Navigate to subscription screen for premium content upgrade
                      await PremiumContentHelper.handlePremiumContentTap(
                        context: context,
                        useUnifiedScreen: true,
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF6C5CE7),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Upgrade to Premium',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Back to Modules',
                style: TextStyle(
                  color: _isDarkMode ? Colors.grey.withValues(alpha: 0.7) : Colors.grey[600],
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage ?? 'Failed to load content',
            style: TextStyle(
              color: _isDarkMode ? Colors.grey.withValues(alpha: 0.7) : Colors.grey[600],
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              _loadTextContent();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6C5CE7),
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildContentView() {
    return Column(
      children: [
        // Content header
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: _isDarkMode ? const Color(0xFF2A2A3E) : Colors.grey[100],
            border: Border(
              bottom: BorderSide(
                color: _isDarkMode
                    ? Colors.white.withValues(alpha: 0.1)
                    : Colors.grey.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      TextContentService.getContentTypeIcon(_textContentData!.contentType),
                      color: const Color(0xFF6C5CE7),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _textContentData!.title,
                          style: TextStyle(
                            color: _isDarkMode ? Colors.white : Colors.black87,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (_textContentData!.description.isNotEmpty) ...[
                          const SizedBox(height: 4),
                          Text(
                            _textContentData!.description,
                            style: TextStyle(
                              color: _isDarkMode
                                  ? Colors.grey.withValues(alpha: 0.8)
                                  : Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFF6C5CE7),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      TextContentService.getContentTypeDisplayName(_textContentData!.contentType),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: TextContentService.getDifficultyColor(_textContentData!.difficulty),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _textContentData!.difficulty.toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        size: 16,
                        color: _isDarkMode
                            ? Colors.grey.withValues(alpha: 0.6)
                            : Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        TextContentService.formatReadingTime(_textContentData!.estimatedReadingTime),
                        style: TextStyle(
                          color: _isDarkMode
                              ? Colors.grey.withValues(alpha: 0.6)
                              : Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: 16),
                  Row(
                    children: [
                      Icon(
                        Icons.text_fields,
                        size: 16,
                        color: _isDarkMode
                            ? Colors.grey.withValues(alpha: 0.6)
                            : Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${_textContentData!.wordCount} words',
                        style: TextStyle(
                          color: _isDarkMode
                              ? Colors.grey.withValues(alpha: 0.6)
                              : Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),

        // Content area
        Expanded(
          child: _buildFormattedContent(),
        ),
      ],
    );
  }

  Widget _buildFormattedContent() {
    final content = _textContentData!.content;
    final format = _textContentData!.contentFormat.toLowerCase();

    return SizedBox(
      width: double.infinity,
      child: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.all(20),
        child: _getContentWidget(content, format),
      ),
    );
  }

  Widget _getContentWidget(String content, String format) {
    final textColor = _isDarkMode ? Colors.white : Colors.black87;

    switch (format) {
      case 'markdown':
        return MarkdownBody(
          data: content,
          styleSheet: MarkdownStyleSheet(
            p: TextStyle(
              color: textColor,
              fontSize: _fontSize,
              height: 1.6,
            ),
            h1: TextStyle(
              color: textColor,
              fontSize: _fontSize + 8,
              fontWeight: FontWeight.bold,
              height: 1.4,
            ),
            h2: TextStyle(
              color: textColor,
              fontSize: _fontSize + 6,
              fontWeight: FontWeight.bold,
              height: 1.4,
            ),
            h3: TextStyle(
              color: textColor,
              fontSize: _fontSize + 4,
              fontWeight: FontWeight.bold,
              height: 1.4,
            ),
            code: TextStyle(
              color: const Color(0xFF6C5CE7),
              fontSize: _fontSize - 2,
              fontFamily: 'monospace',
              backgroundColor: _isDarkMode
                  ? Colors.grey.withValues(alpha: 0.2)
                  : Colors.grey.withValues(alpha: 0.1),
            ),
            blockquote: TextStyle(
              color: textColor.withValues(alpha: 0.8),
              fontSize: _fontSize,
              fontStyle: FontStyle.italic,
            ),
            listBullet: TextStyle(
              color: const Color(0xFF6C5CE7),
              fontSize: _fontSize,
            ),
          ),
        );

      default:
        return Text(
          content,
          style: TextStyle(
            color: textColor,
            fontSize: _fontSize,
            height: 1.6,
          ),
        );
    }
  }

  void _showTextSettings() {
    showModalBottomSheet(
      context: context,
      backgroundColor: _isDarkMode ? const Color(0xFF2A2A3E) : Colors.white,
      isScrollControlled: true,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Text Settings',
                style: TextStyle(
                  color: _isDarkMode ? Colors.white : Colors.black87,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                'Font Size',
                style: TextStyle(
                  color: _isDarkMode ? Colors.white : Colors.black87,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  IconButton(
                    onPressed: _fontSize > 12 ? () {
                      setState(() {
                        _fontSize = (_fontSize - 2).clamp(12.0, 24.0);
                      });
                      setModalState(() {}); // Update modal state
                    } : null,
                    icon: const Icon(Icons.remove),
                    color: _fontSize > 12 ? const Color(0xFF6C5CE7) : Colors.grey,
                  ),
                  Expanded(
                    child: Slider(
                      value: _fontSize,
                      min: 12,
                      max: 24,
                      divisions: 6,
                      activeColor: const Color(0xFF6C5CE7),
                      onChanged: (value) {
                        setState(() {
                          _fontSize = value;
                        });
                        setModalState(() {}); // Update modal state
                      },
                    ),
                  ),
                  IconButton(
                    onPressed: _fontSize < 24 ? () {
                      setState(() {
                        _fontSize = (_fontSize + 2).clamp(12.0, 24.0);
                      });
                      setModalState(() {}); // Update modal state
                    } : null,
                    icon: const Icon(Icons.add),
                    color: _fontSize < 24 ? const Color(0xFF6C5CE7) : Colors.grey,
                  ),
                ],
              ),
              Text(
                'Preview: ${_fontSize.toInt()}px',
                style: TextStyle(
                  color: _isDarkMode ? Colors.grey.withValues(alpha: 0.7) : Colors.grey[600],
                  fontSize: _fontSize,
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}