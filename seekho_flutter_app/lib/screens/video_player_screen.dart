import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import '../models/video.dart';
import '../services/content_service.dart';
import '../utils/premium_content_helper.dart';
import '../services/user_service.dart';

class VideoPlayerScreen extends StatefulWidget {
  final Video video;
  final List<Video> playlist;
  final int initialIndex;

  const VideoPlayerScreen({
    super.key,
    required this.video,
    required this.playlist,
    this.initialIndex = 0,
  });

  @override
  State<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  late PageController _pageController;
  late List<VideoPlayerController> _controllers;
  int _currentIndex = 0;
  bool _isControlsVisible = true;
  bool _isFavorite = false;
  bool _isBookmarked = false;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);
    _controllers = [];
    _initializeControllers();
    _hideSystemUI();
    _recordVideoView();
  }

  void _hideSystemUI() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
  }

  void _showSystemUI() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }

  Future<void> _initializeControllers() async {
    for (int i = 0; i < widget.playlist.length; i++) {
      final video = widget.playlist[i];

      // Check if video is premium and user has subscription
      if (video.isPremium) {
        // Check premium access using the centralized helper
        if (mounted) {
          final hasAccess = await PremiumContentHelper.handleVideoAccess(
            context: context,
            isPremium: true,
            videoTitle: video.title,
            useUnifiedScreen: true,
            popCurrentScreen: true, // Pop the video player if no access
          );

          if (!hasAccess) {
            return; // User was redirected to subscription screen
          }
        }
      }

      // Get video stream URL from backend
      print('🎬 Fetching stream URL for video: ${video.id} - ${video.title}');
      final streamData = await ContentService.getVideoStreamUrl(video.id);
      String videoUrl;

      if (streamData != null && streamData['streamUrl'] != null) {
        videoUrl = streamData['streamUrl'];
        print('✅ Backend returned stream URL: $videoUrl');
        print('🔍 Stream URL type: ${videoUrl.runtimeType}');
        print('🔍 Stream URL length: ${videoUrl.length}');
        print('🔍 Stream URL starts with: ${videoUrl.substring(0, videoUrl.length > 50 ? 50 : videoUrl.length)}...');

        // Validate URL format
        try {
          final uri = Uri.parse(videoUrl);
          print('✅ URL parsed successfully: ${uri.scheme}://${uri.host}${uri.path}');
        } catch (e) {
          print('❌ Invalid URL format: $e');
        }
      } else {
        // Use a working demo video URL
        videoUrl = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
        print('⚠️ No stream URL from backend, using fallback: $videoUrl');
        print('📊 Backend response: $streamData');
      }

      print('🎥 Creating VideoPlayerController with URL: $videoUrl');
      final controller = VideoPlayerController.networkUrl(Uri.parse(videoUrl));
      _controllers.add(controller);

      // Initialize the current video controller
      if (i == _currentIndex) {
        try {
          print('🔄 Initializing video controller...');
          await controller.initialize();
          print('✅ Video controller initialized successfully');
          print('📊 Video info - Duration: ${controller.value.duration}, Size: ${controller.value.size}');

          // Add a small delay to ensure proper initialization
          await Future.delayed(const Duration(milliseconds: 500));

          // Force a rebuild to show the video
          if (mounted) {
            setState(() {});
          }

          controller.play();
          controller.setLooping(true);
          print('▶️ Video playback started for: ${video.title}');
        } catch (e) {
          print('❌ Error initializing video controller: $e');
          print('📍 Error type: ${e.runtimeType}');
          print('📍 Stack trace: ${StackTrace.current}');
        }
      }
    }
  }



  Future<void> _recordVideoView() async {
    await ContentService.recordVideoView(widget.playlist[_currentIndex].id);
  }

  @override
  void dispose() {
    _showSystemUI();
    for (final controller in _controllers) {
      controller.dispose();
    }
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int index) {
    if (_currentIndex < _controllers.length) {
      _controllers[_currentIndex].pause();
    }

    setState(() {
      _currentIndex = index;
    });

    if (index < _controllers.length) {
      final controller = _controllers[index];
      if (controller.value.isInitialized) {
        controller.play();
      } else {
        _initializeVideoAtIndex(index);
      }
    }

    _recordVideoView();
  }

  Future<void> _initializeVideoAtIndex(int index) async {
    if (index >= _controllers.length) return;

    final video = widget.playlist[index];
    final streamData = await ContentService.getVideoStreamUrl(video.id);

    if (streamData != null && streamData['streamUrl'] != null) {
      final controller = _controllers[index];
      await controller.initialize();
      controller.play();
      controller.setLooping(true);

      if (mounted) {
        setState(() {});
      }
    }
  }

  void _togglePlayPause() {
    if (_currentIndex < _controllers.length) {
      final controller = _controllers[_currentIndex];
      if (controller.value.isInitialized) {
        if (controller.value.isPlaying) {
          controller.pause();
        } else {
          controller.play();
        }
        setState(() {});
      }
    }
  }

  void _toggleControls() {
    setState(() {
      _isControlsVisible = !_isControlsVisible;
    });
  }

  Future<void> _toggleFavorite() async {
    final video = widget.playlist[_currentIndex];
    final success = _isFavorite
        ? await UserService.removeFromFavorites(video.id)
        : await UserService.addToFavorites(video.id);

    if (success) {
      setState(() {
        _isFavorite = !_isFavorite;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isFavorite ? 'Added to favorites' : 'Removed from favorites'),
            duration: const Duration(seconds: 1),
          ),
        );
      }
    }
  }

  Future<void> _toggleBookmark() async {
    final video = widget.playlist[_currentIndex];
    final success = _isBookmarked
        ? await UserService.removeBookmark(video.id)
        : await UserService.addBookmark(video.id);

    if (success) {
      setState(() {
        _isBookmarked = !_isBookmarked;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isBookmarked ? 'Bookmarked' : 'Bookmark removed'),
            duration: const Duration(seconds: 1),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _toggleControls,
        child: PageView.builder(
          controller: _pageController,
          scrollDirection: Axis.vertical,
          onPageChanged: _onPageChanged,
          itemCount: widget.playlist.length,
          itemBuilder: (context, index) {
            return _buildVideoPage(index);
          },
        ),
      ),
    );
  }

  Widget _buildVideoPage(int index) {
    final video = widget.playlist[index];
    final controller = index < _controllers.length ? _controllers[index] : null;

    return Stack(
      fit: StackFit.expand,
      children: [
        // Video Player
        if (controller != null && controller.value.isInitialized)
          Center(
            child: AspectRatio(
              aspectRatio: controller.value.aspectRatio,
              child: VideoPlayer(controller),
            ),
          )
        else
          Container(
            color: Colors.grey[900],
            child: const Center(
              child: CircularProgressIndicator(color: Colors.white),
            ),
          ),

        // Play/Pause overlay
        if (_isControlsVisible)
          Center(
            child: GestureDetector(
              onTap: _togglePlayPause,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.5),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  controller != null && controller.value.isPlaying
                      ? Icons.pause
                      : Icons.play_arrow,
                  color: Colors.white,
                  size: 40,
                ),
              ),
            ),
          ),

        // Video info and controls
        if (_isControlsVisible) _buildVideoInfo(video),

        // Top controls
        if (_isControlsVisible) _buildTopControls(),

        // Right side controls
        if (_isControlsVisible) _buildRightControls(video),
      ],
    );
  }

  Widget _buildTopControls() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 10,
      left: 16,
      right: 16,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back, color: Colors.white, size: 28),
          ),
          IconButton(
            onPressed: () => _shareVideo(widget.playlist[_currentIndex]),
            icon: const Icon(Icons.share, color: Colors.white, size: 24),
          ),
        ],
      ),
    );
  }

  Widget _buildRightControls(Video video) {
    return Positioned(
      right: 16,
      bottom: 100,
      child: Column(
        children: [
          // Favorite button
          IconButton(
            onPressed: _toggleFavorite,
            icon: Icon(
              _isFavorite ? Icons.favorite : Icons.favorite_border,
              color: _isFavorite ? Colors.red : Colors.white,
              size: 32,
            ),
          ),
          const SizedBox(height: 20),

          // Bookmark button
          IconButton(
            onPressed: _toggleBookmark,
            icon: Icon(
              _isBookmarked ? Icons.bookmark : Icons.bookmark_border,
              color: _isBookmarked ? Colors.orange : Colors.white,
              size: 32,
            ),
          ),
          const SizedBox(height: 20),

          // Comment button
          IconButton(
            onPressed: () => _showComments(widget.playlist[_currentIndex]),
            icon: const Icon(Icons.comment, color: Colors.white, size: 32),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoInfo(Video video) {
    return Positioned(
      left: 16,
      right: 80,
      bottom: 50,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            video.title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          Text(
            video.description,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 12),

          // Video progress indicator
          if (_currentIndex < _controllers.length &&
              _controllers[_currentIndex].value.isInitialized)
            VideoProgressIndicator(
              _controllers[_currentIndex],
              allowScrubbing: true,
              colors: const VideoProgressColors(
                playedColor: Colors.white,
                bufferedColor: Colors.white30,
                backgroundColor: Colors.white10,
              ),
            ),
        ],
      ),
    );
  }

  void _shareVideo(Video video) {
    try {
      // For now, show a share dialog since share_plus might not be available
      // In a real implementation, you would use: Share.share(shareText);

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: const Color(0xFF2A2A3E),
            title: const Text(
              'Share Video',
              style: TextStyle(color: Colors.white),
            ),
            content: Text(
              'Share "${video.title}" with others',
              style: const TextStyle(color: Colors.white70),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Video link copied to clipboard'),
                      backgroundColor: Color(0xFF6C5CE7),
                    ),
                  );
                },
                child: const Text('Copy Link'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to share video'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showComments(Video video) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF2A2A3E),
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text(
                  'Comments',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.comment_outlined,
                      size: 64,
                      color: Colors.grey.withValues(alpha: 0.5),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Comments coming soon',
                      style: TextStyle(
                        color: Colors.grey.withValues(alpha: 0.7),
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Share your thoughts about this video',
                      style: TextStyle(
                        color: Colors.grey.withValues(alpha: 0.5),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
