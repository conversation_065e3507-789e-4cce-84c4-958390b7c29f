import 'package:flutter/material.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import '../models/subscription_response.dart';
import '../services/new_subscription_service.dart';
import '../services/auth_service.dart';
import 'main_content_screen.dart';

class UnifiedSubscriptionScreen extends StatefulWidget {
  const UnifiedSubscriptionScreen({super.key});

  @override
  State<UnifiedSubscriptionScreen> createState() => _UnifiedSubscriptionScreenState();
}

class _UnifiedSubscriptionScreenState extends State<UnifiedSubscriptionScreen> {
  late Razorpay _razorpay;

  bool _isLoading = true;
  bool _isProcessingPayment = false;
  String? _errorMessage;

  // Data from APIs
  SubscriptionPlansResponse? _plansResponse;
  TrialEligibilityResponse? _trialEligibility;
  SubscriptionStatusResponse? _subscriptionStatus;

  // Selected plan
  SubscriptionPlanData? _selectedPlan;

  // Store payment type for verification
  bool _isRecurringPayment = false;

  // FAQ expansion state
  Set<int> _expandedFAQs = {};

  @override
  void initState() {
    super.initState();
    _initializeRazorpay();
    _loadSubscriptionData();
  }

  void _initializeRazorpay() {
    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }

  Future<void> _loadSubscriptionData() async {
    print('🔄 Loading subscription data...');

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Load all data in parallel
      final results = await Future.wait([
        NewSubscriptionService.getSubscriptionPlans(),
        NewSubscriptionService.checkTrialEligibility(),
        NewSubscriptionService.getSubscriptionStatus(),
      ]);

      setState(() {
        _plansResponse = results[0] as SubscriptionPlansResponse;
        _trialEligibility = results[1] as TrialEligibilityResponse;
        _subscriptionStatus = results[2] as SubscriptionStatusResponse;
        _isLoading = false;
      });

      print('✅ Subscription data loaded successfully');
      print('📋 Plans: ${_plansResponse?.subscriptionList.length}');
      print('📋 Trial eligible: ${_trialEligibility?.data?.isTrialEligible}');
      print('📋 Has subscription: ${_subscriptionStatus?.data?.hasSubscription}');

    } catch (e) {
      print('❌ Error loading subscription data: $e');
      setState(() {
        _errorMessage = 'Failed to load subscription data: $e';
        _isLoading = false;
      });
    }
  }

  List<SubscriptionPlanData> _getAvailablePlans() {
    if (_plansResponse == null) return [];

    final plans = _plansResponse!.subscriptionList;
    final isTrialEligible = _trialEligibility?.data?.isTrialEligible ?? false;

    if (isTrialEligible) {
      // Show trial option for monthly plan only
      return plans.where((plan) => plan.freeTrial).toList();
    } else {
      // Show regular monthly and yearly plans
      return plans;
    }
  }



  Future<void> _selectPlan(SubscriptionPlanData plan) async {
    setState(() => _selectedPlan = plan);

    final isTrialEligible = _trialEligibility?.data?.isTrialEligible ?? false;

    if (isTrialEligible && plan.freeTrial) {
      await _createTrialSubscription();
    } else {
      await _createRegularSubscription(plan);
    }
  }

  Future<void> _createTrialSubscription() async {
    print('🔄 Creating trial subscription...');

    try {
      setState(() => _isProcessingPayment = true);

      final user = await AuthService.getStoredUser();
      if (user == null) {
        throw Exception('User not found');
      }

      final response = await NewSubscriptionService.createTrialWithMandate(
        name: user.name,
        email: user.email,
        phone: '', // Hardcoded phone number
      );

      if (response.success && response.data != null) {
        print('✅ Trial subscription created successfully');
        // await _openRazorpayForTrial(response.data!);
      } else {
        throw Exception(response.message ?? 'Failed to create trial subscription');
      }
    } catch (e) {
      print('❌ Error creating trial subscription: $e');
      setState(() => _isProcessingPayment = false);
      _showErrorSnackBar('Failed to create trial subscription: $e');
    }
  }

  Future<void> _createRegularSubscription(SubscriptionPlanData plan) async {
    print('🔄 Creating regular subscription...');

    // Show payment type selection dialog
    final paymentType = await _showPaymentTypeDialog();
    if (paymentType == null) {
      return; // User cancelled
    }

    try {
      setState(() => _isProcessingPayment = true);

      // Store payment type for verification
      _isRecurringPayment = paymentType == 'recurring';

      final response = await NewSubscriptionService.createRegularSubscription(
        planType: plan.planType,
        recurring: paymentType == 'recurring',
      );

      if (response.success && response.data != null) {
        print('✅ Regular subscription created successfully');
        await _openRazorpayForRegular(response.data!, paymentType == 'recurring');
      } else {
        throw Exception(response.message ?? 'Failed to create subscription');
      }
    } catch (e) {
      print('❌ Error creating regular subscription: $e');
      setState(() => _isProcessingPayment = false);
      _showErrorSnackBar('Failed to create subscription: $e');
    }
  }

  // Future<void> _openRazorpayForTrial(TrialWithMandateData data) async {
  //   try {
  //     final user = await AuthService.getStoredUser();

  //     final options = {
  //       'key': data.razorpayKeyId,
  //       // 'amount': data.trialAmount, // ₹1 = 100 paise
  //       'currency': 'INR',
  //       'name': 'English Guru  App',
  //       'description': data.description,
  //       // 'subscription_id': data.razorpaySubscriptionId,
  //       // 'recurring': true,
  //       'prefill': {
  //         'contact': '**********',
  //         'email': user?.email ?? '<EMAIL>',
  //       },
  //       'theme': {
  //         'color': '#F7B801'
  //       },
  //       'method': {
  //         'upi': true,
  //         'card': true,
  //         'netbanking': true,
  //         'wallet': true,
  //       },

  //     };

  //     print('🎯 Opening Razorpay for trial with options: $options');
  //     _razorpay.open(options);
  //   } catch (e) {
  //     setState(() => _isProcessingPayment = false);
  //     _showErrorSnackBar('Failed to open payment: $e');
  //   }
  // }

  Future<void> _openRazorpayForRegular(RegularSubscriptionData data, bool isRecurring) async {
    try {
      final user = await AuthService.getStoredUser();

      final options = <String, dynamic>{
        'key': data.razorpayKeyId,
        'currency': data.currency,
        'name': 'English Guru App',
        'description': '${_selectedPlan!.displayName} ${isRecurring ? 'Subscription' : 'Payment'}',
        'prefill': {
          'contact': '**********',
          'email': user?.email ?? '<EMAIL>',
        },
        'theme': {
          'color': '#F7B801'
        },
        'method': {
          'upi': true,
          'card': true,
          'netbanking': true,
          'wallet': true,
        },
      };

      // Add appropriate ID based on payment type
      if (isRecurring && data.razorpaySubscriptionId != null) {
        options['subscription_id'] = data.razorpaySubscriptionId;
        print('🎯 Opening Razorpay for recurring subscription with subscription_id: ${data.razorpaySubscriptionId}');
      } else if (!isRecurring && data.razorpayOrderId != null) {
        options['order_id'] = data.razorpayOrderId;
        options['amount'] = data.amount;
        print('🎯 Opening Razorpay for one-time payment with order_id: ${data.razorpayOrderId}');
      } else {
        throw Exception('Invalid payment data: missing ${isRecurring ? 'razorpaySubscriptionId' : 'razorpayOrderId'}');
      }

      print('🎯 Opening Razorpay with options: $options');
      _razorpay.open(options);
    } catch (e) {
      setState(() => _isProcessingPayment = false);
      _showErrorSnackBar('Failed to open payment: $e');
    }
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) async {
    print('✅ Payment successful: ${response.paymentId}');
    print('🔍 Payment response data: ${response.data}');
    print('🔍 Payment response orderId: ${response.orderId}');
    print('🔍 Payment response signature: ${response.signature}');
    print('🔍 Is recurring payment: $_isRecurringPayment');

    try {
      // Determine payment type and use appropriate verification method
      Map<String, dynamic> verificationResult;

      if (_isRecurringPayment) {
        // For recurring subscriptions, extract subscription_id from response data
        String? subscriptionId;

        // Check if response.data contains razorpay_subscription_id
        if (response.data != null && response.data!['razorpay_subscription_id'] != null) {
          subscriptionId = response.data!['razorpay_subscription_id'];
          print('🔍 Found subscription_id in response.data: $subscriptionId');
        } else {
          // Fallback to orderId field (some versions might use this)
          subscriptionId = response.orderId;
          print('🔍 Using orderId as fallback for subscription_id: $subscriptionId');
        }

        if (subscriptionId == null || subscriptionId.isEmpty) {
          print('❌ ERROR: Subscription ID not found in payment response');
          print('❌ Response data: ${response.data}');
          print('❌ Response orderId: ${response.orderId}');
          throw Exception('Subscription ID not found in payment response. Please contact support.');
        }

        verificationResult = await NewSubscriptionService.verifyRecurringPayment(
          paymentId: response.paymentId ?? '',
          subscriptionId: subscriptionId,
          signature: response.signature ?? '',
          plan: _selectedPlan?.planType ?? '',
        );
        print('🔄 Verified recurring payment with subscription_id: $subscriptionId');
      } else {
        // For one-time payments, extract order_id from response data
        String? orderId;

        // Check if response.data contains razorpay_order_id
        if (response.data != null && response.data!['razorpay_order_id'] != null) {
          orderId = response.data!['razorpay_order_id'];
          print('🔍 Found order_id in response.data: $orderId');
        } else {
          // Fallback to orderId field
          orderId = response.orderId;
          print('🔍 Using orderId as fallback for order_id: $orderId');
        }

        if (orderId == null || orderId.isEmpty) {
          print('❌ ERROR: Order ID not found in payment response');
          print('❌ Response data: ${response.data}');
          print('❌ Response orderId: ${response.orderId}');
          throw Exception('Order ID not found in payment response. Please contact support.');
        }

        verificationResult = await NewSubscriptionService.verifyOneTimePayment(
          paymentId: response.paymentId ?? '',
          orderId: orderId,
          signature: response.signature ?? '',
          plan: _selectedPlan?.planType ?? '',
        );
        print('💳 Verified one-time payment with order_id: $orderId');
      }

      if (verificationResult['success'] == true) {
        print('✅ Payment verified successfully');
        _showSuccessDialog();
      } else {
        throw Exception(verificationResult['message'] ?? 'Payment verification failed');
      }
    } catch (e) {
      print('❌ Payment verification failed: $e');
      _showErrorSnackBar('Payment verification failed: $e');
    } finally {
      setState(() => _isProcessingPayment = false);
    }
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    print('❌ Payment failed: ${response.code} - ${response.message}');
    setState(() => _isProcessingPayment = false);
    _showErrorSnackBar('Payment failed: ${response.message}');
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    print('📱 External wallet selected: ${response.walletName}');
    // External wallets like UPI apps are supported
    // The payment will be handled by the external app
    // We don't need to show an error here
    setState(() => _isProcessingPayment = false);
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }

  Future<String?> _showPaymentTypeDialog() async {
    return showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2A2A2A),
        title: const Text(
          'Choose Payment Type',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'How would you like to pay?',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 20),
            _buildPaymentOption(
              title: 'Auto-Recurring Subscription',
              description: 'Automatic monthly/yearly billing',
              icon: Icons.autorenew,
              value: 'recurring',
              recommended: true,
            ),
            const SizedBox(height: 12),
            _buildPaymentOption(
              title: 'One-Time Payment',
              description: 'Pay once, renew manually',
              icon: Icons.payment,
              value: 'one-time',
              recommended: false,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel', style: TextStyle(color: Colors.white70)),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentOption({
    required String title,
    required String description,
    required IconData icon,
    required String value,
    required bool recommended,
  }) {
    return InkWell(
      onTap: () => Navigator.of(context).pop(value),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFF1A1A1A),
          borderRadius: BorderRadius.circular(8),
          border: recommended
              ? Border.all(color: const Color(0xFFF7B801), width: 2)
              : Border.all(color: Colors.transparent),
        ),
        child: Row(
          children: [
            Icon(icon, color: const Color(0xFFF7B801), size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (recommended) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: const Color(0xFFF7B801),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Text(
                            'RECOMMENDED',
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Success!'),
        content: const Text('Your subscription has been activated successfully!'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              // Navigate to main content screen
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const MainContentScreen()),
              );
            },
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      body: _isLoading ? _buildLoadingWidget() : _buildContent(),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Color(0xFFF7B801)),
          SizedBox(height: 16),
          Text(
            'Loading subscription plans...',
            style: TextStyle(color: Colors.white70),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_errorMessage != null) {
      return _buildErrorWidget();
    }

    final availablePlans = _getAvailablePlans();
    if (availablePlans.isEmpty) {
      return _buildEmptyWidget();
    }

    return Stack(
      children: [
        // Main content
        SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: Column(
            children: [
              // Close button
              SafeArea(
                child: Align(
                  alignment: Alignment.topRight,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.3),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              // 50% OFF Badge
              _buildOfferBadge(),
              const SizedBox(height: 20),

              // Main heading
              _buildMainHeading(),
              const SizedBox(height: 30),

              // Features section
              _buildFeaturesSection(),
              const SizedBox(height: 30),

              // FAQ section
              _buildFAQSection(),

              // Gradient fade to indicate scrollable content ends
              Container(
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.1),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 350), // Extra space to avoid overlap with bottom pricing card
            ],
          ),
        ),

        // Bottom sticky section
        _buildBottomSection(availablePlans),

        // Processing overlay
        if (_isProcessingPayment) _buildProcessingOverlay(),
      ],
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 64),
          const SizedBox(height: 16),
          Text(
            _errorMessage!,
            style: const TextStyle(color: Colors.white70),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadSubscriptionData,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFF7B801),
              foregroundColor: Colors.black,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inbox_outlined, color: Colors.white70, size: 64),
          SizedBox(height: 16),
          Text(
            'No subscription plans available',
            style: TextStyle(color: Colors.white70),
          ),
        ],
      ),
    );
  }

  Widget _buildOfferBadge() {
    return Center(
      child: Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFFFD700).withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '50%',
              style: TextStyle(
                color: Colors.white,
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'OFF',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
                letterSpacing: 2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainHeading() {
    return Column(
      children: [
        const Text(
          'ONE TIME OFFER !',
          style: TextStyle(
            color: Colors.white,
            fontSize: 28,
            fontWeight: FontWeight.bold,
            letterSpacing: 1.5,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'You\'ll never see this again !',
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 18,
            fontStyle: FontStyle.italic,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 20),

        // Awards section
        // Row(
        //   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        //   children: [
        //     _buildAward('Best AI\nAPP\'23'),
        //     _buildAward('4.7 Star\nratings'),
        //   ],
        // ),
      ],
    );
  }

  Widget _buildAward(String text) {
    return Column(
      children: [
        // Laurel wreath icon
        Container(
          width: 60,
          height: 40,
          decoration: BoxDecoration(
            color: const Color(0xFFFFD700).withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.emoji_events,
            color: Color(0xFFFFD700),
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          text,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildFeaturesSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(
                Icons.star,
                color: Color(0xFFFFD700),
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'What you will get',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          _buildFeatureItem(
            icon: Icons.play_circle_filled,
            title: 'Access Over 100 Full-Length English practice lessons',
          ),
          const SizedBox(height: 16),

          // _buildFeatureItem(
          //   icon: Icons.person,
          //   title: 'Have calls with Sarah anytime',
          // ),
          // const SizedBox(height: 16),

          _buildFeatureItem(
            icon: Icons.trending_up,
            title: 'Boost Your Progress in English with 300+ Exercises',
          ),
          const SizedBox(height: 16),

          _buildFeatureItem(
            icon: Icons.cancel_outlined,
            title: 'Cancel Your Plan Anytime',
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem({required IconData icon, required String title}) {
    return Row(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: const Color(0xFFFFD700).withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: const Color(0xFFFFD700),
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const Icon(
          Icons.check,
          color: Color(0xFFFFD700),
          size: 20,
        ),
      ],
    );
  }



  Widget _buildFAQSection() {
    final faqs = [
      {
        'question': 'What is English Guru Pro really and why should I care?',
        'answer': 'English Guru Pro is a comprehensive English learning platform with advanced features, personalized learning paths, unlimited practice sessions, and expert guidance to help you master English faster.',
      },
      {
        'question': 'What is the difference between the free and pro version?',
        'answer': 'Pro version includes unlimited access to all courses, advanced practice exercises, personalized feedback, progress tracking, offline content, and priority support. Free version has limited access to basic content.',
      },
      {
        'question': 'Is my payment secure and Can I cancel my Pro Plan subscription?',
        'answer': 'Yes, all payments are processed through secure payment gateways with bank-level encryption. You can cancel your subscription anytime from your account settings, and access continues until the end of your billing period.',
      },
      {
        'question': 'What to do If I am facing any problems in purchasing?',
        'answer': 'Well, we are always here to talk! Just drop us a message directly at +************ or <EMAIL> and you would get direct assistance from our founders.',
      },
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(
                Icons.star,
                color: Color(0xFFFFD700),
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Frequently Asked Questions',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...faqs.asMap().entries.map((entry) =>
            _buildFAQItem(entry.key, entry.value['question']!, entry.value['answer']!)
          ),
        ],
      ),
    );
  }

  Widget _buildFAQItem(int index, String question, String answer) {
    final isExpanded = _expandedFAQs.contains(index);

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.transparent,
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            if (isExpanded) {
              _expandedFAQs.remove(index);
            } else {
              _expandedFAQs.add(index);
            }
          });
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      question,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color: Colors.white70,
                    size: 24,
                  ),
                ],
              ),
              if (isExpanded) ...[
                const SizedBox(height: 12),
                Text(
                  answer,
                  style: TextStyle(
                    color: Colors.grey[300],
                    fontSize: 14,
                    height: 1.5,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomSection(List<SubscriptionPlanData> plans) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF8B4513).withValues(alpha: 0.8),
              const Color(0xFF8B4513),
            ],
          ),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Plan selection
              Row(
                children: [
                  Expanded(
                    child: _buildPlanOption(
                      plans.firstWhere((p) => p.planType == 'monthly',
                        orElse: () => plans.first),
                      'Monthly',
                      '₹99.00',
                      'Billed & recurring monthly',
                      false,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildPlanOption(
                      plans.firstWhere((p) => p.planType == 'yearly',
                        orElse: () => plans.first),
                      'Yearly',
                      '₹499.00',
                      'Billed & recurring yearly',
                      true,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Referral code link
              // GestureDetector(
              //   onTap: () {
              //     // Handle referral code
              //   },
              //   child: Text(
              //     'Have a referral code?',
              //     style: TextStyle(
              //       color: Colors.white.withValues(alpha: 0.7),
              //       fontSize: 14,
              //       decoration: TextDecoration.underline,
              //     ),
              //   ),
              // ),
              // const SizedBox(height: 16),

              // CTA Button
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: _isProcessingPayment ? null : () => _showPaymentOptionsDialog(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFFD700),
                    foregroundColor: Colors.black,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    _isProcessingPayment
                      ? 'Processing...'
                      : _selectedPlan != null
                        ? 'Get English Guru Pro @ ${_getSelectedPlanPrice()}'
                        : 'Get English Guru Pro',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),

              // // Restore purchases
              // GestureDetector(
              //   onTap: () {
              //     // Handle restore purchases
              //   },
              //   child: Text(
              //     'Restore Purchases',
              //     style: TextStyle(
              //       color: Colors.white.withValues(alpha: 0.7),
              //       fontSize: 14,
              //     ),
              //   ),
              // ),
              // const SizedBox(height: 16),

              // Terms and Privacy
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.6),
                    fontSize: 12,
                  ),
                  children: [
                    const TextSpan(text: 'By continuing, you agree to the English Guru '),
                    TextSpan(
                      text: 'privacy policy',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        decoration: TextDecoration.underline,
                      ),
                    ),
                    const TextSpan(text: ', and '),
                    TextSpan(
                      text: 'terms of service',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlanOption(
    SubscriptionPlanData plan,
    String title,
    String price,
    String description,
    bool isBestDeal,
  ) {
    final isSelected = _selectedPlan?.packageId == plan.packageId;

    // Calculate GST amount
    final basePrice = title == 'Monthly' ? 99.0 : 499.0;
    final gstAmount = "18%";

    return GestureDetector(
      onTap: () => _selectPlan(plan),
      child: Container(
        padding: const EdgeInsets.all(12), // Reduced padding
        decoration: BoxDecoration(
          color: isSelected
            ? const Color(0xFFFFD700).withValues(alpha: 0.2)
            : Colors.black.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(8), // Smaller border radius
          border: Border.all(
            color: isSelected
              ? const Color(0xFFFFD700)
              : isBestDeal
                ? const Color(0xFFFFD700)
                : Colors.transparent,
            width: 2,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min, // Make column take minimum space
          children: [
            if (isBestDeal)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2), // Smaller padding
                decoration: BoxDecoration(
                  color: const Color(0xFFFFD700),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  'Best Deal',
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            if (isBestDeal) const SizedBox(height: 6),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14, // Smaller font
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              '₹${basePrice.toInt()}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18, // Smaller font
                fontWeight: FontWeight.bold,
              ),
            ),
            // GST text - small and less visible
            Text(
              '+₹$gstAmount GST',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.4), // Very low opacity
                fontSize: 8, // Very small font
              ),
            ),
            const SizedBox(height: 2),
            Text(
              description,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.6), // Slightly more visible than GST
                fontSize: 10, // Smaller font
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProcessingOverlay() {
    return Container(
      color: Colors.black.withValues(alpha: 0.7),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Color(0xFFFFD700)),
            SizedBox(height: 16),
            Text(
              'Processing payment...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getSelectedPlanPrice() {
    if (_selectedPlan == null) return '';

    final basePrice = _selectedPlan!.planType == 'monthly' ? 99 : 499;
    return '₹$basePrice';
  }

  void _showPaymentOptionsDialog() {
    if (_selectedPlan == null) {
      _showErrorSnackBar('Please select a plan first');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: const Color(0xFF1A1A1A),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Choose Payment Type',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Selected: ${_selectedPlan!.displayName} - ${_getSelectedPlanPrice()}',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.7),
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 24),

              _buildPaymentOption(
                title: 'Auto-Recurring Subscription',
                description: 'Automatic ${_selectedPlan!.planType} billing',
                icon: Icons.autorenew,
                value: 'recurring',
                recommended: true,
              ),
              const SizedBox(height: 12),
              _buildPaymentOption(
                title: 'One-Time Payment',
                description: 'Pay once, renew manually',
                icon: Icons.payment,
                value: 'one-time',
                recommended: false,
              ),
            ],
          ),
        ),
      ),
    ).then((paymentType) {
      if (paymentType != null) {
        _handlePurchaseWithPaymentType(paymentType);
      }
    });
  }

  void _handlePurchaseWithPaymentType(String paymentType) {
    if (_selectedPlan != null) {
      // Set the payment type preference
      _isRecurringPayment = paymentType == 'recurring';
      _selectPlan(_selectedPlan!);
    }
  }

  @override
  void dispose() {
    _razorpay.clear();
    super.dispose();
  }
}
