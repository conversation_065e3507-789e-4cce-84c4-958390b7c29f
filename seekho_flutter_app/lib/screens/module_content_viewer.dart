import 'package:flutter/material.dart';
import '../models/module_models.dart';
import '../models/video.dart';
import '../services/modules_service.dart';
import '../services/progress_service.dart';
import '../utils/premium_content_helper.dart';
import 'video_player_screen.dart';
import 'mcq_viewer_screen.dart';
import 'text_content_viewer.dart';
import 'questionnaire_viewer_screen.dart';

class ModuleContentViewer extends StatefulWidget {
  final LearningModule module;

  const ModuleContentViewer({
    super.key,
    required this.module,
  });

  @override
  State<ModuleContentViewer> createState() => _ModuleContentViewerState();
}

class _ModuleContentViewerState extends State<ModuleContentViewer> {
  LearningModule? _moduleWithContent;
  bool _isLoading = true;
  int _currentContentIndex = 0;
  final PageController _pageController = PageController();
  Map<String, ContentProgress> _contentProgressMap = {};
  DateTime _contentStartTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    _loadModuleContent();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadModuleContent() async {
    try {
      debugPrint('🚀 LOADING MODULE CONTENT:');
      debugPrint('🆔 Module ID: ${widget.module.id}');
      debugPrint('📝 Module Title: ${widget.module.title}');

      final moduleWithContent = await ModulesService.getModuleWithContent(widget.module.id);

      debugPrint('📦 MODULE CONTENT LOADED:');
      debugPrint('✅ Module received: ${moduleWithContent != null}');

      if (moduleWithContent != null) {
        debugPrint('📄 Populated content: ${moduleWithContent.populatedContent?.length ?? 0} items');

        if (moduleWithContent.populatedContent != null) {
          for (int i = 0; i < moduleWithContent.populatedContent!.length; i++) {
            final content = moduleWithContent.populatedContent![i];
            debugPrint('📄 Content $i: ${content.contentType} (ID: ${content.contentId})');
          }
        }
      } else {
        debugPrint('❌ Module content is NULL');
      }

      // Load progress for all content items
      if (moduleWithContent?.populatedContent != null) {
        for (final content in moduleWithContent!.populatedContent!) {
          final progress = await ProgressService.getContentProgress(content.contentId);
          if (progress != null) {
            _contentProgressMap[content.contentId] = progress;
          }
        }
      }

      setState(() {
        _moduleWithContent = moduleWithContent;
        _isLoading = false;
      });

      debugPrint('🎯 UI STATE UPDATED: loading=false, hasContent=${moduleWithContent != null}');

      // Start tracking time for current content
      _startContentTracking();
    } catch (e) {
      debugPrint('💥 ERROR LOADING MODULE CONTENT: $e');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading content: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _startContentTracking() {
    _contentStartTime = DateTime.now();

    // Record that user started viewing this content (only if no existing progress)
    if (_moduleWithContent?.populatedContent != null &&
        _currentContentIndex < _moduleWithContent!.populatedContent!.length) {
      final currentContent = _moduleWithContent!.populatedContent![_currentContentIndex];

      // Only record 0.0 progress if there's no existing progress to avoid overwriting
      final existingProgress = _contentProgressMap[currentContent.contentId];
      if (existingProgress == null || existingProgress.progressPercentage == 0.0) {
        _recordContentProgress(currentContent, 0.0);
      }
    }
  }

  Future<void> _recordContentProgress(ModuleContent content, double progressPercentage) async {
    final timeSpent = DateTime.now().difference(_contentStartTime).inSeconds;
    final contentType = _getContentType(content.contentType);

    await ProgressService.recordContentProgress(
      contentId: content.contentId,
      contentType: contentType,
      progressPercentage: progressPercentage,
      timeSpent: timeSpent,
      metadata: {
        'moduleId': widget.module.id,
        'contentTitle': content.contentData?.title ?? '',
        'contentOrder': content.order,
      },
    );

    // Update local progress map
    final updatedProgress = await ProgressService.getContentProgress(content.contentId);
    if (updatedProgress != null) {
      setState(() {
        _contentProgressMap[content.contentId] = updatedProgress;
      });
    }
  }

  /// Refresh progress after MCQ completion
  Future<void> _refreshProgressAfterMCQ(ModuleContent content) async {
    // Get the updated progress from the progress service
    final updatedProgress = await ProgressService.getContentProgress(content.contentId);
    if (updatedProgress != null) {
      setState(() {
        _contentProgressMap[content.contentId] = updatedProgress;
      });
    }
  }

  ContentType _getContentType(String contentTypeString) {
    switch (contentTypeString.toLowerCase()) {
      case 'video':
        return ContentType.video;
      case 'mcq':
        return ContentType.mcq;
      case 'questionnaire':
        return ContentType.questionnaire;
      default:
        return ContentType.text;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: const Color(0xFF2D1B69),
        foregroundColor: Colors.white,
        title: Text(
          widget.module.title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF6C5CE7),
              ),
            )
          : _moduleWithContent == null
              ? _buildErrorState()
              : _buildContentViewer(),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load content',
            style: TextStyle(
              color: Colors.grey.withValues(alpha: 0.7),
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Please check your connection and try again',
            style: TextStyle(
              color: Colors.grey.withValues(alpha: 0.5),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              _loadModuleContent();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6C5CE7),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildContentViewer() {
    final populatedContent = _moduleWithContent!.populatedContent;

    debugPrint('🎨 BUILDING CONTENT VIEWER:');
    debugPrint('📄 Populated content: ${populatedContent?.length ?? 0} items');

    if (populatedContent != null) {
      for (int i = 0; i < populatedContent.length; i++) {
        final content = populatedContent[i];
        debugPrint('📄 Content $i: ${content.contentType} (ID: ${content.contentId})');
      }
    }

    if (populatedContent == null || populatedContent.isEmpty) {
      debugPrint('❌ NO CONTENT TO DISPLAY');
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.content_copy_outlined,
              size: 64,
              color: Colors.grey.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No content available',
              style: TextStyle(
                color: Colors.grey.withValues(alpha: 0.7),
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'This module doesn\'t have any content yet',
              style: TextStyle(
                color: Colors.grey.withValues(alpha: 0.5),
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Progress indicator
        _buildProgressIndicator(populatedContent),

        // Content area
        Expanded(
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              // Don't automatically mark content as completed when navigating away
              // Content should only be marked as completed when actually finished

              setState(() {
                _currentContentIndex = index;
              });

              // Start tracking new content
              _startContentTracking();
            },
            itemCount: populatedContent.length,
            itemBuilder: (context, index) {
              final content = populatedContent[index];
              return _buildContentItem(content);
            },
          ),
        ),

        // Navigation controls
        _buildNavigationControls(populatedContent),
      ],
    );
  }

  Widget _buildProgressIndicator(List<ModuleContent> content) {
    // Calculate overall progress based on actual progress percentages
    double totalProgress = 0.0;
    for (final contentItem in content) {
      final progress = _contentProgressMap[contentItem.contentId];
      if (progress != null) {
        totalProgress += progress.progressPercentage;
      }
    }

    final overallProgress = content.isNotEmpty ? (totalProgress / (content.length * 100.0)) : 0.0;
    final currentContentProgress = _contentProgressMap[content[_currentContentIndex].contentId];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A3E),
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'Progress: ${_currentContentIndex + 1} of ${content.length}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: ProgressService.getProgressColor(
                    currentContentProgress?.status ?? ProgressStatus.notStarted
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      ProgressService.getProgressIcon(
                        currentContentProgress?.status ?? ProgressStatus.notStarted
                      ),
                      size: 12,
                      color: ProgressService.getProgressColor(
                        currentContentProgress?.status ?? ProgressStatus.notStarted
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${(overallProgress * 100).round()}%',
                      style: TextStyle(
                        color: ProgressService.getProgressColor(
                          currentContentProgress?.status ?? ProgressStatus.notStarted
                        ),
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: overallProgress,
            backgroundColor: Colors.grey.withValues(alpha: 0.3),
            valueColor: AlwaysStoppedAnimation<Color>(
              ProgressService.getProgressColor(
                currentContentProgress?.status ?? ProgressStatus.notStarted
              ),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Text(
                  content[_currentContentIndex].contentData?.title ?? 'Content ${_currentContentIndex + 1}',
                  style: TextStyle(
                    color: Colors.grey.withValues(alpha: 0.7),
                    fontSize: 14,
                  ),
                ),
              ),
              if (currentContentProgress != null && currentContentProgress.timeSpent > 0) ...[
                Icon(
                  Icons.access_time,
                  size: 12,
                  color: Colors.grey.withValues(alpha: 0.6),
                ),
                const SizedBox(width: 4),
                Text(
                  ProgressService.formatTimeSpent(currentContentProgress.timeSpent),
                  style: TextStyle(
                    color: Colors.grey.withValues(alpha: 0.6),
                    fontSize: 12,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContentItem(ModuleContent content) {
    debugPrint('🏗️ BUILDING CONTENT ITEM:');
    debugPrint('🆔 Content ID: ${content.contentId}');
    debugPrint('🏷️ Content Type: ${content.contentType}');
    debugPrint('🏷️ Content Type (lowercase): ${content.contentType.toLowerCase()}');

    switch (content.contentType.toLowerCase()) {
      case 'video':
        debugPrint('📹 Building VIDEO content');
        return _buildVideoContent(content);
      case 'questionnaire':
        debugPrint('❓ Building QUESTIONNAIRE content');
        return _buildQuestionnaireContent(content);
      case 'mcq':
        debugPrint('🧠 Building MCQ content');
        return _buildMCQContent(content);
      case 'text':
      case 'summary':
      case 'reading':
      case 'instructions':
      case 'notes':
        debugPrint('📝 Building TEXT content');
        return _buildTextContent(content);
      default:
        debugPrint('❌ Building UNSUPPORTED content');
        return _buildUnsupportedContent(content);
    }
  }

  Widget _buildVideoContent(ModuleContent content) {
    final videoData = content.contentData?.rawData;
    final thumbnail = videoData?['thumbnail'] ?? '';
    final duration = videoData?['duration'] ?? 0;
    final isPremium = content.contentData?.isPremium ?? false;

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Video thumbnail with play button
          GestureDetector(
            onTap: () => _playVideo(content),
            child: Container(
              width: double.infinity,
              height: 200,
              decoration: BoxDecoration(
                color: const Color(0xFF2A2A3E),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.1),
                ),
                image: thumbnail.isNotEmpty
                    ? DecorationImage(
                        image: NetworkImage(thumbnail),
                        fit: BoxFit.cover,
                      )
                    : null,
              ),
              child: Stack(
                children: [
                  // Dark overlay for better visibility
                  if (thumbnail.isNotEmpty)
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.black.withValues(alpha: 0.3),
                      ),
                    ),
                  // Play button
                  Center(
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFF6C5CE7).withValues(alpha: 0.9),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.play_arrow,
                        size: 48,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  // Premium badge
                  if (isPremium)
                    Positioned(
                      top: 12,
                      right: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.orange,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.lock, size: 12, color: Colors.white),
                            SizedBox(width: 4),
                            Text(
                              'Premium',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  // Duration badge
                  if (duration > 0)
                    Positioned(
                      bottom: 12,
                      right: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _formatDuration(duration),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            content.contentData?.title ?? 'Video Content',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          if (content.contentData?.rawData['description'] != null)
            Text(
              content.contentData!.rawData['description'],
              style: TextStyle(
                color: Colors.grey.withValues(alpha: 0.8),
                fontSize: 14,
              ),
            ),
          const SizedBox(height: 16),
          // Video info
          Row(
            children: [
              Icon(
                Icons.play_circle_outline,
                size: 16,
                color: Colors.grey.withValues(alpha: 0.6),
              ),
              const SizedBox(width: 4),
              Text(
                'Video Lesson',
                style: TextStyle(
                  color: Colors.grey.withValues(alpha: 0.6),
                  fontSize: 12,
                ),
              ),
              if (duration > 0) ...[
                const SizedBox(width: 16),
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: Colors.grey.withValues(alpha: 0.6),
                ),
                const SizedBox(width: 4),
                Text(
                  _formatDuration(duration),
                  style: TextStyle(
                    color: Colors.grey.withValues(alpha: 0.6),
                    fontSize: 12,
                  ),
                ),
              ],
              if (isPremium) ...[
                const SizedBox(width: 16),
                Icon(
                  Icons.lock,
                  size: 16,
                  color: Colors.orange.withValues(alpha: 0.8),
                ),
                const SizedBox(width: 4),
                Text(
                  'Premium',
                  style: TextStyle(
                    color: Colors.orange.withValues(alpha: 0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
  Widget _buildQuestionnaireContent(ModuleContent content) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF2A2A3E),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.1),
                ),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.assignment_outlined,
                    size: 48,
                    color: Color(0xFF6C5CE7),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    content.contentData?.title ?? 'Questionnaire',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  if (content.contentData?.metadata?['totalQuestions'] != null)
                    Text(
                      '${content.contentData!.metadata!['totalQuestions']} questions • ${content.contentData?.estimatedTime ?? 10} min',
                      style: TextStyle(
                        color: Colors.grey.withValues(alpha: 0.7),
                        fontSize: 14,
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Interactive Questionnaire',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Questionnaire viewer coming soon',
              style: TextStyle(
                color: Colors.grey.withValues(alpha: 0.7),
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () async {
                final isPremium = content.contentData?.isPremium ?? false;
                final questionnaireTitle = content.contentData?.title ?? 'Questionnaire';

                // Check premium access before opening questionnaire
                final hasAccess = await PremiumContentHelper.handleQuestionnaireAccess(
                  context: context,
                  isPremium: isPremium,
                  questionnaireTitle: questionnaireTitle,
                );

                if (!hasAccess) {
                  return; // User was redirected to subscription screen
                }

                if (mounted) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => QuestionnaireViewerScreen(
                        questionnaireId: content.contentId,
                        title: questionnaireTitle,
                      ),
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF6C5CE7),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: const Text('Start Questionnaire'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMCQContent(ModuleContent content) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF2A2A3E),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.1),
                ),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.quiz,
                    size: 48,
                    color: Color(0xFF6C5CE7),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    content.contentData?.title ?? 'Multiple Choice Quiz',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (content.contentData?.metadata?['totalQuestions'] != null)
                        Text(
                          '${content.contentData!.metadata!['totalQuestions']} questions',
                          style: TextStyle(
                            color: Colors.grey.withValues(alpha: 0.7),
                            fontSize: 14,
                          ),
                        ),
                      if (content.contentData?.rawData['passingScore'] != null) ...[
                        Text(
                          ' • Pass: ${content.contentData!.rawData['passingScore']}%',
                          style: TextStyle(
                            color: Colors.grey.withValues(alpha: 0.7),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Multiple Choice Quiz',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Quiz viewer coming soon',
              style: TextStyle(
                color: Colors.grey.withValues(alpha: 0.7),
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () async {
                final isPremium = content.contentData?.isPremium ?? false;
                final mcqTitle = content.contentData?.title ?? 'Quiz';

                // Check premium access before opening MCQ
                final hasAccess = await PremiumContentHelper.handleMCQAccess(
                  context: context,
                  isPremium: isPremium,
                  mcqTitle: mcqTitle,
                );

                if (!hasAccess) {
                  return; // User was redirected to subscription screen
                }

                if (mounted) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => MCQViewerScreen(
                        mcqId: content.contentId,
                        title: mcqTitle,
                        onProgressUpdate: () {
                          // Refresh progress when MCQ is completed
                          _refreshProgressAfterMCQ(content);
                        },
                      ),
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF6C5CE7),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: const Text('Start Quiz'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextContent(ModuleContent content) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF2A2A3E),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.1),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.article_outlined,
                        color: Color(0xFF6C5CE7),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        content.contentData?.title ?? 'Text Content',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  ModulesService.getContentTypeDisplayName(content.contentType),
                  style: TextStyle(
                    color: const Color(0xFF6C5CE7),
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.article,
                    size: 64,
                    color: Color(0xFF6C5CE7),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Text Content',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Text content viewer coming soon',
                    style: TextStyle(
                      color: Colors.grey.withValues(alpha: 0.7),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () async {
                      debugPrint('🔥 READ CONTENT BUTTON PRESSED!');
                      debugPrint('🆔 Content ID: ${content.contentId}');
                      debugPrint('🏷️ Content Type: ${content.contentType}');
                      debugPrint('📝 Content Title: ${content.contentData?.title}');

                      final isPremium = content.contentData?.isPremium ?? false;
                      final contentTitle = content.contentData?.title ?? 'Text Content';

                      // Check premium access before opening text content
                      final hasAccess = await PremiumContentHelper.handleTextContentAccess(
                        context: context,
                        isPremium: isPremium,
                        contentTitle: contentTitle,
                      );

                      if (!hasAccess) {
                        return; // User was redirected to subscription screen
                      }

                      if (mounted) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => TextContentViewer(content: content),
                          ),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF6C5CE7),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: const Text('Read Content'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnsupportedContent(ModuleContent content) {
    debugPrint('🚨 UNSUPPORTED CONTENT CALLED:');
    debugPrint('🆔 Content ID: ${content.contentId}');
    debugPrint('🏷️ Content Type: ${content.contentType}');

    // Handle text content that might have fallen through
    if (content.contentType.toLowerCase() == 'text') {
      debugPrint('🔄 Redirecting text content to _buildTextContentDirect');
      return _buildTextContentDirect(content);
    }

    return Container(
      padding: const EdgeInsets.all(20),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.help_outline,
              size: 64,
              color: Colors.grey.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'Unsupported Content Type',
              style: TextStyle(
                color: Colors.grey.withValues(alpha: 0.7),
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Content type "${content.contentType}" is not yet supported',
              style: TextStyle(
                color: Colors.grey.withValues(alpha: 0.5),
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextContentDirect(ModuleContent content) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF2A2A3E),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.1),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.article_outlined,
                        color: Color(0xFF6C5CE7),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        content.contentData?.title ?? 'Text Content',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  'Text Content',
                  style: TextStyle(
                    color: const Color(0xFF6C5CE7),
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.article,
                    size: 64,
                    color: Color(0xFF6C5CE7),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Text Content',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Ready to read',
                    style: TextStyle(
                      color: Colors.grey.withValues(alpha: 0.7),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () async {
                      debugPrint('🔥 TEXT CONTENT BUTTON PRESSED!');
                      debugPrint('🆔 Content ID: ${content.contentId}');
                      debugPrint('🏷️ Content Type: ${content.contentType}');
                      debugPrint('📝 Content Title: ${content.contentData?.title}');

                      final isPremium = content.contentData?.isPremium ?? false;
                      final contentTitle = content.contentData?.title ?? 'Text Content';

                      // Check premium access before opening text content
                      final hasAccess = await PremiumContentHelper.handleTextContentAccess(
                        context: context,
                        isPremium: isPremium,
                        contentTitle: contentTitle,
                      );

                      if (!hasAccess) {
                        return; // User was redirected to subscription screen
                      }

                      if (mounted) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => TextContentViewer(content: content),
                          ),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF6C5CE7),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: const Text('Start Learning'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationControls(List<ModuleContent> content) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A3E),
        border: Border(
          top: BorderSide(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Previous button
          Expanded(
            child: ElevatedButton(
              onPressed: _currentContentIndex > 0
                  ? () {
                      _pageController.previousPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: _currentContentIndex > 0
                    ? const Color(0xFF6C5CE7).withValues(alpha: 0.2)
                    : Colors.grey.withValues(alpha: 0.2),
                foregroundColor: _currentContentIndex > 0
                    ? const Color(0xFF6C5CE7)
                    : Colors.grey,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.arrow_back, size: 18),
                  SizedBox(width: 8),
                  Text('Previous'),
                ],
              ),
            ),
          ),
          const SizedBox(width: 16),
          // Next/Complete button
          Expanded(
            child: ElevatedButton(
              onPressed: _currentContentIndex < content.length - 1
                  ? () {
                      _pageController.nextPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                  : () {
                      // Complete module and exit
                      _completeModule();
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF6C5CE7),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(_currentContentIndex < content.length - 1 ? 'Next' : 'Complete'),
                  const SizedBox(width: 8),
                  Icon(
                    _currentContentIndex < content.length - 1
                        ? Icons.arrow_forward
                        : Icons.check,
                    size: 18,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _completeModule() async {
    // Record progress for the current content as completed
    if (_moduleWithContent?.populatedContent != null &&
        _currentContentIndex < _moduleWithContent!.populatedContent!.length) {
      final currentContent = _moduleWithContent!.populatedContent![_currentContentIndex];
      await _recordContentProgress(currentContent, 100.0);
    }

    // Show completion dialog
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          backgroundColor: const Color(0xFF2A2A3E),
          title: const Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 28),
              SizedBox(width: 12),
              Text(
                'Module Complete!',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
          content: Text(
            'Congratulations! You have completed "${widget.module.title}".',
            style: const TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Exit module viewer
              },
              child: const Text(
                'Continue',
                style: TextStyle(color: Color(0xFF6C5CE7)),
              ),
            ),
          ],
        ),
      );
    }
  }

  Future<void> _playVideo(ModuleContent content) async {
    final videoData = content.contentData?.rawData;
    if (videoData == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Video data not available'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final isPremium = content.contentData?.isPremium ?? false;
    final videoTitle = content.contentData?.title ?? 'Video';

    // Check premium access before playing video
    final hasAccess = await PremiumContentHelper.handleVideoAccess(
      context: context,
      isPremium: isPremium,
      videoTitle: videoTitle,
      useUnifiedScreen: true,
      popCurrentScreen: false,
    );

    if (!hasAccess) {
      return; // User was redirected to subscription screen
    }

    // Convert module video content to Video model for existing player
    final video = Video(
      id: content.contentId,
      title: videoTitle,
      description: videoData['description'] ?? '',
      thumbnail: videoData['thumbnail'],
      videoUrl: videoData['videoUrl'],
      duration: videoData['duration'] ?? 0,
      isPremium: isPremium,
      isActive: true,
      topicId: videoData['topicId'] ?? '',
      categoryId: videoData['categoryId'] ?? '',
      order: content.order,
      views: videoData['views'] ?? 0,
      createdAt: DateTime.parse(videoData['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(videoData['updatedAt'] ?? DateTime.now().toIso8601String()),
    );

    // Navigate to video player with single video playlist
    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VideoPlayerScreen(
            video: video,
            playlist: [video],
            initialIndex: 0,
          ),
        ),
      );
    }
  }

  String _formatDuration(int seconds) {
    if (seconds <= 0) return '0:00';

    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final remainingSeconds = seconds % 60;

    if (hours > 0) {
      return '$hours:${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
    } else {
      return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
    }
  }
}