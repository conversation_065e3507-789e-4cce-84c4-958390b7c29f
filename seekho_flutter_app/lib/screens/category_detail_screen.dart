import 'package:flutter/material.dart';
import '../models/category.dart';
import '../models/topic.dart';
import '../models/video.dart';
import '../services/content_service.dart';
import 'video_detail_screen.dart';
import 'topic_detail_screen.dart';
import 'subscription_screen.dart';
import '../widgets/premium_lock_icon.dart';

class CategoryDetailScreen extends StatefulWidget {
  final Category category;

  const CategoryDetailScreen({
    super.key,
    required this.category,
  });

  @override
  State<CategoryDetailScreen> createState() => _CategoryDetailScreenState();
}

class _CategoryDetailScreenState extends State<CategoryDetailScreen> {
  List<Topic> _topics = [];
  List<Video> _videos = [];

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCategoryData();
  }

  Future<void> _loadCategoryData() async {
    setState(() => _isLoading = true);

    try {
      // Use the new comprehensive endpoint for better performance
      final response = await ContentService.getCategoryComplete(widget.category.id);

      if (response.success && response.data != null) {
        setState(() {
          _topics = response.data!.topics;
          _videos = response.data!.videos;
          _isLoading = false;
        });
      } else {
        // Fallback to individual API calls if comprehensive endpoint fails
        await _loadCategoryDataFallback();
      }
    } catch (e) {
      // Fallback to individual API calls
      await _loadCategoryDataFallback();
    }
  }

  Future<void> _loadCategoryDataFallback() async {
    try {
      // Load topics and videos for this category in parallel
      final results = await Future.wait([
        _loadTopicsForCategory(),
        ContentService.getVideosByCategory(widget.category.id),
      ]);

      final topicsResponse = results[0] as TopicsResponse;
      final videosResponse = results[1] as VideosResponse;

      setState(() {
        _topics = topicsResponse.data ?? [];
        _videos = videosResponse.data ?? [];
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading content: $e')),
        );
      }
    }
  }

  Future<TopicsResponse> _loadTopicsForCategory() async {
    try {
      // First try the specific endpoint for topics in category
      final topicsResponse = await ContentService.getTopicsInCategory(widget.category.id);

      // If that fails or returns no data, try getting all topics and filter by category
      if (!topicsResponse.success || (topicsResponse.data?.isEmpty ?? true)) {
        final allTopicsResponse = await ContentService.getAllTopics();

        if (allTopicsResponse.success && allTopicsResponse.data != null) {
          final filteredTopics = allTopicsResponse.data!
              .where((topic) => topic.categoryId == widget.category.id)
              .toList();

          return TopicsResponse(
            success: true,
            message: 'Topics loaded via fallback',
            data: filteredTopics,
          );
        }
      }

      return topicsResponse;
    } catch (e) {
      return TopicsResponse(
        success: false,
        message: 'Failed to load topics: $e',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF1A1A1A),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.category.name,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.star_border, color: Colors.white),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Favorites feature coming soon!')),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Search feature coming soon!')),
              );
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadCategoryData,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Topics chips
                    if (_topics.isNotEmpty) _buildTopicsChips(),

                    const SizedBox(height: 24),

                    // Top videos section
                    _buildTopVideosSection(),

                    const SizedBox(height: 100), // Space for bottom nav
                  ],
                ),
              ),
            ),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  Widget _buildTopicsChips() {
    if (_topics.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Wrap(
          spacing: 12,
          runSpacing: 8,
          children: _topics.map((topic) => _buildTopicChip(topic)).toList(),
        ),
      ],
    );
  }

  Widget _buildTopicChip(Topic topic) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TopicDetailScreen(
              topic: topic,
              category: widget.category,
            ),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          topic.name,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  Widget _buildTopVideosSection() {
    if (_videos.isEmpty) {
      return const Center(
        child: Text(
          'No videos available',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(
              Icons.bar_chart,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Top 10 in ${widget.category.name}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 0.75,
          ),
          itemCount: _videos.take(10).length,
          itemBuilder: (context, index) {
            return _buildNumberedVideoCard(_videos[index], index + 1);
          },
        ),
      ],
    );
  }

  Widget _buildNumberedVideoCard(Video video, int number) {
    // Define gradient colors for variety
    final gradients = [
      [const Color(0xFF4CAF50), const Color(0xFF8BC34A)], // Green
      [const Color(0xFF00BCD4), const Color(0xFF009688)], // Teal
      [const Color(0xFFE91E63), const Color(0xFF9C27B0)], // Pink/Purple
      [const Color(0xFF4CAF50), const Color(0xFF66BB6A)], // Green variant
    ];

    final gradient = gradients[(number - 1) % gradients.length];

    return GestureDetector(
      onTap: () {
        if (video.isPremium) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const SubscriptionScreen(),
            ),
          );
        } else {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => VideoDetailScreen(
                video: video,
                category: widget.category,
                allVideos: _videos,
              ),
            ),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: gradient,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Stack(
          children: [
            // Premium lock overlay
            Positioned(
              top: 8,
              right: 8,
              child: PremiumLockIcon(
                isPremium: video.isPremium,
                size: 16,
                padding: const EdgeInsets.all(6),
                borderRadius: 20,
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Category badge
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'EARNING APP REVIEW',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const Spacer(),
                  // Large number
                  Text(
                    '$number',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Title
                  Text(
                    video.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildBottomNavigation() {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFF1A1A1A),
        border: Border(
          top: BorderSide(color: Color(0xFF3A3A3A), width: 0.5),
        ),
      ),
      child: BottomNavigationBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        currentIndex: 0,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Colors.white,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.fiber_new),
            label: 'New',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.video_library),
            label: 'My Library',
          ),
        ],
      ),
    );
  }
}
