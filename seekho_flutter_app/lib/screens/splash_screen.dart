import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/auth_service.dart';
import '../services/new_subscription_service.dart';
import 'login_screen.dart';
import 'main_content_screen.dart';
import 'subscription_intro_screen.dart';
import 'onboarding_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _checkAuthenticationStatus();
  }

  /// Check if user has completed onboarding locally
  Future<bool> _hasCompletedOnboardingLocally(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('onboarding_complete_$userId') ?? false;
    } catch (e) {
      print('Error checking local onboarding status: $e');
      return false;
    }
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.elasticOut),
    ));

    _animationController.forward();
  }

  Future<void> _checkAuthenticationStatus() async {
    // Wait for animation to complete
    await Future.delayed(const Duration(milliseconds: 2500));

    if (!mounted) return;

    try {
      // Check if user is logged in
      final isLoggedIn = await AuthService.isLoggedIn();
      
      if (!isLoggedIn) {
        // User not logged in, go to login screen
        _navigateToLogin();
        return;
      }

      // User is logged in, get user data
      final user = await AuthService.getStoredUser();
      
      if (user == null) {
        // No user data found, go to login screen
        _navigateToLogin();
        return;
      }

      // Check onboarding status - user is considered onboarded if they have completed onboarding
      // OR if they have all essential data (for backward compatibility)
      final hasEssentialData = user.phoneNumber != null &&
                               user.phoneNumber!.isNotEmpty &&
                               user.classLevel != null &&
                               user.age != null;

      // Also check local onboarding completion flag for additional verification
      final hasCompletedOnboardingLocally = await _hasCompletedOnboardingLocally(user.id);

      final isOnboarded = user.isOnboardingComplete || hasEssentialData || hasCompletedOnboardingLocally;

      if (!isOnboarded) {
        // User needs to complete onboarding
        _navigateToOnboarding(user);
        return;
      }

      // User has completed onboarding, check premium status
      final hasPremiumAccess = await NewSubscriptionService.hasAccessToPremiumContent();
      
      if (hasPremiumAccess) {
        // Premium user goes directly to main content
        _navigateToMainContent();
      } else {
        // Free user sees subscription intro
        _navigateToSubscriptionIntro();
      }
    } catch (e) {
      print('Error checking authentication status: $e');
      // On error, go to login screen
      _navigateToLogin();
    }
  }

  void _navigateToLogin() {
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
      );
    }
  }

  void _navigateToOnboarding(user) {
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => OnboardingScreen(user: user)),
      );
    }
  }

  void _navigateToMainContent() {
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const MainContentScreen()),
      );
    }
  }

  void _navigateToSubscriptionIntro() {
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const SubscriptionIntroScreen()),
      );
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      body: Center(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // App Logo
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(60),
                        gradient: const LinearGradient(
                          colors: [Color(0xFF6C5CE7), Color(0xFF2D1B69)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.school,
                        color: Colors.white,
                        size: 60,
                      ),
                    ),
                    const SizedBox(height: 32),
                    
                    // App Name
                    const Text(
                      'English Guru',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 1.2,
                      ),
                    ),
                    const SizedBox(height: 8),
                    
                    // Tagline
                    Text(
                      'Master English with AI',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 16,
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                    const SizedBox(height: 48),
                    
                    // Loading indicator
                    SizedBox(
                      width: 40,
                      height: 40,
                      child: CircularProgressIndicator(
                        strokeWidth: 3,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          const Color(0xFFFFD700).withValues(alpha: 0.8),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
