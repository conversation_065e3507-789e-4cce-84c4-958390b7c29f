import 'package:flutter/material.dart';
import '../models/module_models.dart';
import '../services/offline_service.dart';
import '../services/modules_service.dart';

class OfflineContentScreen extends StatefulWidget {
  const OfflineContentScreen({super.key});

  @override
  State<OfflineContentScreen> createState() => _OfflineContentScreenState();
}

class _OfflineContentScreenState extends State<OfflineContentScreen> {
  List<LearningModule> _availableModules = [];
  List<CachedContent> _cachedContent = [];
  Map<String, CacheStatus> _cacheStatusMap = {};
  Map<String, double> _downloadProgress = {};
  bool _isLoading = true;
  int _totalCacheSize = 0;
  bool _isOnline = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Check online status
      final isOnline = await OfflineService.isOnline();

      // Load available modules
      final modules = await ModulesService.getModulesForCurrentUser();

      // Load cached content
      final cachedContent = await OfflineService.getAllCachedContent();

      // Get cache status for each module
      final cacheStatusMap = <String, CacheStatus>{};
      for (final module in modules) {
        final status = await OfflineService.getCacheStatus(module.id);
        cacheStatusMap[module.id] = status;
      }

      // Get total cache size
      final totalSize = await OfflineService.getCacheSize();

      setState(() {
        _availableModules = modules;
        _cachedContent = cachedContent;
        _cacheStatusMap = cacheStatusMap;
        _totalCacheSize = totalSize;
        _isOnline = isOnline;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _downloadModule(LearningModule module) async {
    if (!_isOnline) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No internet connection. Cannot download content.'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _cacheStatusMap[module.id] = CacheStatus.caching;
      _downloadProgress[module.id] = 0.0;
    });

    try {
      // Simulate download progress
      for (int i = 0; i <= 100; i += 10) {
        await Future.delayed(const Duration(milliseconds: 100));
        setState(() {
          _downloadProgress[module.id] = i / 100.0;
        });
      }

      final success = await OfflineService.cacheModule(module);

      setState(() {
        _cacheStatusMap[module.id] = success ? CacheStatus.cached : CacheStatus.failed;
        _downloadProgress.remove(module.id);
      });

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${module.title} downloaded successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        _loadData(); // Refresh data
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to download ${module.title}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _cacheStatusMap[module.id] = CacheStatus.failed;
        _downloadProgress.remove(module.id);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error downloading ${module.title}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: const Color(0xFF2D1B69),
        foregroundColor: Colors.white,
        title: const Text(
          'Offline Content',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(_isOnline ? Icons.cloud_done : Icons.cloud_off),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(_isOnline ? 'Online' : 'Offline'),
                  backgroundColor: _isOnline ? Colors.green : Colors.red,
                ),
              );
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF6C5CE7),
              ),
            )
          : Column(
              children: [
                // Cache info header
                _buildCacheInfoHeader(),

                // Content tabs
                Expanded(
                  child: DefaultTabController(
                    length: 2,
                    child: Column(
                      children: [
                        TabBar(
                          labelColor: const Color(0xFF6C5CE7),
                          unselectedLabelColor: Colors.grey,
                          indicatorColor: const Color(0xFF6C5CE7),
                          tabs: const [
                            Tab(text: 'Available'),
                            Tab(text: 'Downloaded'),
                          ],
                        ),
                        Expanded(
                          child: TabBarView(
                            children: [
                              _buildAvailableContent(),
                              _buildDownloadedContent(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildCacheInfoHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A3E),
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.storage,
                color: const Color(0xFF6C5CE7),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Cache Usage: ${OfflineService.formatCacheSize(_totalCacheSize)}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: _showClearCacheDialog,
                child: const Text(
                  'Clear All',
                  style: TextStyle(
                    color: Color(0xFF6C5CE7),
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: _totalCacheSize / (500 * 1024 * 1024), // 500MB max
            backgroundColor: Colors.grey.withValues(alpha: 0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF6C5CE7)),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Text(
                '${_cachedContent.length} items cached',
                style: TextStyle(
                  color: Colors.grey.withValues(alpha: 0.7),
                  fontSize: 12,
                ),
              ),
              const Spacer(),
              Text(
                'Max: 500MB',
                style: TextStyle(
                  color: Colors.grey.withValues(alpha: 0.7),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAvailableContent() {
    if (_availableModules.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.download_outlined,
              size: 64,
              color: Colors.grey.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No modules available',
              style: TextStyle(
                color: Colors.grey.withValues(alpha: 0.7),
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _availableModules.length,
      itemBuilder: (context, index) {
        final module = _availableModules[index];
        final cacheStatus = _cacheStatusMap[module.id] ?? CacheStatus.notCached;
        final downloadProgress = _downloadProgress[module.id];

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A3E),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          module.title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          module.description,
                          style: TextStyle(
                            color: Colors.grey.withValues(alpha: 0.8),
                            fontSize: 14,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  _buildDownloadButton(module, cacheStatus, downloadProgress),
                ],
              ),
              if (downloadProgress != null) ...[
                const SizedBox(height: 12),
                LinearProgressIndicator(
                  value: downloadProgress,
                  backgroundColor: Colors.grey.withValues(alpha: 0.3),
                  valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF6C5CE7)),
                ),
                const SizedBox(height: 4),
                Text(
                  'Downloading... ${(downloadProgress * 100).toInt()}%',
                  style: TextStyle(
                    color: Colors.grey.withValues(alpha: 0.7),
                    fontSize: 12,
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildDownloadButton(LearningModule module, CacheStatus status, double? progress) {
    switch (status) {
      case CacheStatus.cached:
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.check_circle,
                size: 16,
                color: const Color(0xFF4CAF50),
              ),
              const SizedBox(width: 4),
              const Text(
                'Downloaded',
                style: TextStyle(
                  color: Color(0xFF4CAF50),
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        );

      case CacheStatus.caching:
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: const Color(0xFF6C5CE7),
                ),
              ),
              const SizedBox(width: 6),
              const Text(
                'Downloading',
                style: TextStyle(
                  color: Color(0xFF6C5CE7),
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        );

      default:
        return ElevatedButton(
          onPressed: _isOnline ? () => _downloadModule(module) : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF6C5CE7),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            minimumSize: Size.zero,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.download, size: 16),
              const SizedBox(width: 4),
              const Text('Download', style: TextStyle(fontSize: 12)),
            ],
          ),
        );
    }
  }

  Widget _buildDownloadedContent() {
    if (_cachedContent.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cloud_download_outlined,
              size: 64,
              color: Colors.grey.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No downloaded content',
              style: TextStyle(
                color: Colors.grey.withValues(alpha: 0.7),
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _cachedContent.length,
      itemBuilder: (context, index) {
        final content = _cachedContent[index];

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A3E),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getContentTypeIcon(content.contentType),
                  color: const Color(0xFF4CAF50),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      content.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      content.contentType.toUpperCase(),
                      style: TextStyle(
                        color: Colors.grey.withValues(alpha: 0.6),
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  IconData _getContentTypeIcon(String contentType) {
    switch (contentType.toLowerCase()) {
      case 'video':
        return Icons.play_circle_outline;
      case 'mcq':
        return Icons.quiz;
      case 'questionnaire':
        return Icons.assignment_outlined;
      default:
        return Icons.article;
    }
  }
  void _showClearCacheDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2A2A3E),
        title: const Text(
          'Clear All Cache',
          style: TextStyle(color: Colors.white),
        ),
        content: Text(
          'Are you sure you want to clear all downloaded content? This will free up ${OfflineService.formatCacheSize(_totalCacheSize)} of storage.',
          style: TextStyle(color: Colors.grey.withValues(alpha: 0.8)),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              final success = await OfflineService.clearAllCache();
              if (success && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('All cache cleared successfully!'),
                    backgroundColor: Colors.green,
                  ),
                );
                _loadData();
              } else if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Failed to clear cache'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text(
              'Clear All',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}