import 'package:flutter/material.dart';
import '../models/category.dart';
import 'category_detail_screen.dart';

class AllCategoriesScreen extends StatelessWidget {
  final List<Category> categories;

  const AllCategoriesScreen({
    super.key,
    required this.categories,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF1A1A1A),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'All Categories',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 16,
              mainAxisSpacing: 20,
              childAspectRatio: 0.95,
            ),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              return _buildCategoryItem(context, category);
            },
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomNavigation(context),
    );
  }

  Widget _buildCategoryItem(BuildContext context, Category category) {
    final categoryConfig = _getCategoryConfig(category.name);

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CategoryDetailScreen(category: category),
          ),
        );
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Category Icon Container
          Container(
            width: 55,
            height: 55,
            decoration: BoxDecoration(
              color: categoryConfig['color'],
              shape: BoxShape.circle,
            ),
            child: Center(
              child: categoryConfig['icon'] is String
                  ? Text(
                      categoryConfig['icon'],
                      style: const TextStyle(
                        fontSize: 22,
                        color: Colors.white,
                      ),
                    )
                  : Icon(
                      categoryConfig['icon'],
                      color: Colors.white,
                      size: 22,
                    ),
            ),
          ),
          const SizedBox(height: 6),
          // Category Name
          Flexible(
            child: Text(
              category.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getCategoryConfig(String categoryName) {
    final configs = {
      'Sarkari Kaam': {'icon': '🏛️', 'color': const Color(0xFFFF6B35)},
      'PartTime': {'icon': '💰', 'color': const Color(0xFF4CAF50)},
      'Gaming': {'icon': '🎮', 'color': const Color(0xFFE53E3E)},
      'Instagram': {'icon': '📷', 'color': const Color(0xFFE91E63)},
      'Youtube': {'icon': '▶️', 'color': const Color(0xFFFF0000)},
      'English': {'icon': 'Abc', 'color': const Color(0xFFFFD700)},
      'Astrology': {'icon': '🔮', 'color': const Color(0xFF9C27B0)},
      'Finance': {'icon': '💹', 'color': const Color(0xFF00BCD4)},
      'Business': {'icon': '💼', 'color': const Color(0xFF3F51B5)},
      'Wellness': {'icon': '🌿', 'color': const Color(0xFF4CAF50)},
      'Career & Jobs': {'icon': '🎓', 'color': const Color(0xFF2196F3)},
      'Share Market': {'icon': '📈', 'color': const Color(0xFF9C27B0)},
      'Editing': {'icon': '🎬', 'color': const Color(0xFF673AB7)},
      'Mobile Tricks': {'icon': '📱', 'color': const Color(0xFF2196F3)},
      'Success': {'icon': '👨‍💼', 'color': const Color(0xFFFF9800)},
      'Health': {'icon': '❤️', 'color': const Color(0xFFE91E63)},
      'Knowledge': {'icon': '📚', 'color': const Color(0xFFFF5722)},
      'Crime': {'icon': '🔍', 'color': const Color(0xFFF44336)},
      'Horror': {'icon': '💀', 'color': const Color(0xFF424242)},
      'Devotion': {'icon': '🕉️', 'color': const Color(0xFFFF9800)},
      'Food': {'icon': '🍳', 'color': const Color(0xFFFF9800)},
      'Self-Growth': {'icon': '💡', 'color': const Color(0xFFFFEB3B)},
      'Agriculture': {'icon': '🌾', 'color': const Color(0xFF4CAF50)},
      'Marketing': {'icon': '📢', 'color': const Color(0xFF00BCD4)},
      'Transport': {'icon': '🚗', 'color': const Color(0xFF607D8B)},
      'Rocket': {'icon': '🚀', 'color': const Color(0xFFE91E63)},
      'Design': {'icon': '🎨', 'color': const Color(0xFF9C27B0)},
      'Management': {'icon': '👥', 'color': const Color(0xFF795548)},
    };

    return configs[categoryName] ?? {
      'icon': '📁',
      'color': const Color(0xFF607D8B),
    };
  }

  Widget _buildBottomNavigation(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFF1A1A1A),
        border: Border(
          top: BorderSide(color: Color(0xFF3A3A3A), width: 0.5),
        ),
      ),
      child: BottomNavigationBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        currentIndex: 0, // Home is selected
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Colors.white,
        unselectedItemColor: Colors.grey,
        onTap: (index) {
          if (index == 0) {
            // Home - go back to main screen
            Navigator.pop(context);
          }
          // For other tabs, we could navigate to different screens
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.fiber_new),
            label: 'New',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.video_library),
            label: 'My Library',
          ),
        ],
      ),
    );
  }
}
