import 'package:flutter/material.dart';
import '../utils/premium_content_helper.dart';

class PremiumLockIcon extends StatefulWidget {
  final bool isPremium;
  final double size;
  final EdgeInsets padding;
  final double borderRadius;
  final VoidCallback? onTap;
  final bool enableTap;

  const PremiumLockIcon({
    super.key,
    required this.isPremium,
    this.size = 16,
    this.padding = const EdgeInsets.all(6),
    this.borderRadius = 20,
    this.onTap,
    this.enableTap = true,
  });

  @override
  State<PremiumLockIcon> createState() => _PremiumLockIconState();
}

class _PremiumLockIconState extends State<PremiumLockIcon> {
  bool _hasAccess = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkAccess();
  }

  Future<void> _checkAccess() async {
    if (!widget.isPremium) {
      setState(() {
        _hasAccess = true; // Non-premium content is always accessible
        _isLoading = false;
      });
      return;
    }

    try {
      final hasAccess = await PremiumContentHelper.hasAccess();
      if (mounted) {
        setState(() {
          _hasAccess = hasAccess;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error checking premium access: $e');
      if (mounted) {
        setState(() {
          _hasAccess = false;
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Don't show anything if it's not a premium content
    if (!widget.isPremium) {
      return const SizedBox.shrink();
    }

    // Don't show lock icon if user has access
    if (!_isLoading && _hasAccess) {
      return const SizedBox.shrink();
    }

    // Show lock icon if loading or user doesn't have access
    Widget lockIcon = Container(
      padding: widget.padding,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      child: Icon(
        Icons.lock,
        color: const Color(0xFFFFD700),
        size: widget.size,
      ),
    );

    // Add tap functionality if enabled
    if (widget.enableTap && !_isLoading && !_hasAccess) {
      return GestureDetector(
        onTap: widget.onTap ?? () async {
          // Default tap behavior: navigate to subscription screen
          await PremiumContentHelper.handlePremiumContentTap(
            context: context,
            useUnifiedScreen: true,
          );
        },
        child: lockIcon,
      );
    }

    return lockIcon;
  }
}
