# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Signing configuration (NEVER commit these!)
android/key.properties
android/local.properties
upload-keystore.jks
android/upload-keystore.jks
*.jks
*.keystore
*.p12
*.pem

# Environment variables and sensitive configuration
.env
.env.*
*.env
.env.example

# API keys and secrets
**/secrets/
**/config/secrets.json
**/config/keys.json

# Documentation with sensitive info
# *.md files may contain API keys or sensitive setup instructions
subsciption.md
subscription testing.md
subscription-update.md
team-interagtion.md

# Google Services configuration
android/app/google-services.json
ios/Runner/GoogleService-Info.plist

# Test files with sensitive data
test_subscription.dart
test_trial_subscription.dart

# macOS specific
.DS_Store
**/.DS_Store

# Additional sensitive files
**/postman*.json
**/api_keys.json
**/credentials.json
**/firebase_options.dart
**/firebase_app_id_file.json

# Build and cache directories
build/
.dart_tool/
.packages
.pub/

# IDE files
.vscode/settings.json
.vscode/launch.json
*.code-workspace

# Logs
*.log
logs/
*.log.*

# Temporary files
*.tmp
*.temp
*~

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
