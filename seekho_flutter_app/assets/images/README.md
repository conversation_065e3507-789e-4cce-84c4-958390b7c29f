# Image Assets

This directory contains image assets for the English Guru  app.

## How to add images:

1. **Place your image files in this directory**
   - Supported formats: PNG, JPG, JPEG, GIF, WebP, SVG
   - Recommended: PNG for logos, JPG for photos

2. **Example file structure:**
   ```
   assets/images/
   ├── logo.png
   ├── app_icon.png
   ├── splash_screen.png
   ├── placeholder.png
   └── icons/
       ├── home_icon.png
       └── profile_icon.png
   ```

3. **Use in Flutter code:**
   ```dart
   // For images in assets/images/
   Image.asset('assets/images/logo.png')
   
   // With specific dimensions
   Image.asset(
     'assets/images/logo.png',
     width: 100,
     height: 100,
   )
   
   // As decoration
   Container(
     decoration: BoxDecoration(
       image: DecorationImage(
         image: AssetImage('assets/images/background.png'),
         fit: BoxFit.cover,
       ),
     ),
   )
   ```

4. **Best practices:**
   - Use descriptive file names
   - Optimize image sizes for mobile
   - Consider different screen densities (1x, 2x, 3x)
   - Use PNG for transparency, JPG for photos

## Current assets:
- Add your image files here and update this list
