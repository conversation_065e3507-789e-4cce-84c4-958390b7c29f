#!/bin/bash

# <PERSON>ript to get SHA-1 certificate fingerprint for release keystore
# This is needed to configure Google Sign-In for release APK

echo "Getting SHA-1 certificate fingerprint for release keystore..."
echo "=================================================="

# Check if keystore file exists
if [ ! -f "android/upload-keystore.jks" ]; then
    echo "❌ Error: upload-keystore.jks not found in android/ directory"
    echo "Please make sure your keystore file is in the correct location"
    exit 1
fi

# Get SHA-1 fingerprint
echo "🔑 Extracting SHA-1 fingerprint..."
keytool -list -v -keystore android/upload-keystore.jks -alias upload -storepass Praveen@1 -keypass Praveen@1 | grep "SHA1:"

echo ""
echo "📋 Next steps:"
echo "1. Copy the SHA-1 fingerprint above"
echo "2. Go to Firebase Console: https://console.firebase.google.com/"
echo "3. Select project: learning-app-119d3"
echo "4. Go to Project Settings → Your Apps → Android App"
echo "5. Add the SHA-1 certificate fingerprint"
echo "6. Download the updated google-services.json"
echo "7. Replace android/app/google-services.json with the new file"
echo "8. Rebuild your release APK"
